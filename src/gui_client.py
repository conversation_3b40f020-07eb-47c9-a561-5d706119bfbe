import asyncio
import os
import subprocess
import sys
import threading
import tkinter as tk
from datetime import datetime
from time import sleep
from tkinter import ttk, messagebox
from PIL import ImageGrab, Image, ImageTk
from src import llm_prompt, fill_ocs
from src.llm_client import LLMClient
import base64
import json
import time
from io import BytesIO
from pathlib import Path
from src.online.login import get_captcha, auth
from src.online.ceos_api import save_task_run_record
import shutil
from src.version import VERSION, COPYRIGHT
from src.utils.logger import Logger
from src.dao import app_config_dao
from pyppeteer import launch

class AutoInputClient:
    def __init__(self, root, target_url, title):
        # 初始化日志记录器
        self.logger = Logger.get_logger()

        # 初始化参数配置
        self.init_param()

        # 初始化数据库
        self.app_config_dao = app_config_dao.AppConfigDB()
        self.chrome_path_config = self.app_config_dao.get_config_by_code('browser_path')

        self.root = root
        self.target_url = target_url
        self.title = title
        self.version = VERSION  # 从version.py获取版本信息
        self.copyright = COPYRIGHT  # 从version.py获取版权信息
        self.llm_client = LLMClient()
        self.settings_file = Path(__file__).parent.parent / "settings.json"
        
        # 启用窗口大小调整
        root.resizable(True, True)
        root.title(title + ' - V' + self.version)

        # 设置窗口大小和居中显示
        window_width = 400  # 修改为400
        window_height = 600  # 修改为600
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x_cordinate = int((screen_width / 2) - (window_width / 2))
        y_cordinate = int((screen_height / 2) - (window_height / 2))
        root.geometry(f"{window_width}x{window_height}+{x_cordinate}+{y_cordinate}")
        self.window_width = window_width
        self.window_height = window_height
        
        # 设置最小窗口大小
        root.minsize(400, 600)
        
        # 创建主容器
        self.main_container = tk.Frame(root)
        self.main_container.pack(fill="both", expand=True)
        
        # 创建登录面板
        self.login_frame = tk.Frame(self.main_container)
        self.login_frame.pack(fill="both", expand=True)
        
        # 创建功能面板（初始隐藏）
        self.function_frame = tk.Frame(self.main_container)
        
        # 初始化登录界面
        self.create_login_ui()
        
        # 初始化功能界面
        self.create_function_ui()
        
        # 检查是否已登录
        self.check_login_status()

    def init_param(self):
        self.browser = None
        self.context = None
        self.page = None
        self.captcha_id = None
        self.self_ai_result = None
        self.self_screenshot = None
        self.self_screenshot_path = None
        self.chrome_path_config = None
        self.app_config_dao = None
        self.chrome_port = 19222
        self.chrome_work_dir = os.path.join(os.getcwd(), 'chrome_data')
        os.makedirs(self.chrome_work_dir, exist_ok=True)

    def check_login_status(self):
        """检查是否已登录"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                # 检查是否有token和登录时间
                if 'token' in settings and 'login_time' in settings:
                    token = settings.get('token')
                    login_time = settings.get('login_time')
                    current_time = int(time.time())
                    
                    # 检查token是否有效（24小时内）
                    if token and login_time and (current_time - login_time < 24 * 60 * 60):
                        # 加载保存的用户名
                        if 'username' in settings:
                            self.username_input.insert(0, settings['username'])
                            self.remember_var.set(True)
                        
                        # 自动登录
                        self.show_function_ui()
                        return
            
            # 如果没有有效的登录信息，显示登录界面
            self.load_saved_username()
        except Exception as e:
            self.logger.debug(f"检查登录状态失败: {str(e)}")

    def load_saved_username(self):
        """加载保存的用户名"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    if 'username' in settings:
                        self.username_input.insert(0, settings['username'])
                        self.remember_var.set(True)
        except Exception as e:
            self.logger.debug(f"加载保存的用户名失败: {str(e)}")

    def create_login_ui(self):
        """创建登录界面"""
        # 创建主框架
        main_frame = tk.Frame(self.login_frame, padx=30, pady=30)
        main_frame.pack(fill="both", expand=True)
        
        # 添加标题
        title_label = tk.Label(main_frame, text="系统登录", font=("Arial", 24, "bold"), fg="#333")
        title_label.pack(pady=(0, 20))
        
        # 添加分隔线
        separator = ttk.Separator(main_frame, orient="horizontal")
        separator.pack(fill="x", pady=(0, 20))
        
        # 用户名输入框
        username_frame = tk.Frame(main_frame)
        username_frame.pack(fill="x", pady=(0, 10))
        
        username_label = tk.Label(username_frame, text="用户名【招商随行ID】", font=("Arial", 12), fg="#555")
        username_label.pack(anchor="w")
        
        self.username_input = tk.Entry(username_frame, font=("Arial", 12))
        self.username_input.pack(fill="x", ipady=8)
        
        # 密码输入框
        password_frame = tk.Frame(main_frame)
        password_frame.pack(fill="x", pady=(0, 10))
        
        password_label = tk.Label(password_frame, text="密码", font=("Arial", 12), fg="#555")
        password_label.pack(anchor="w")
        
        self.password_input = tk.Entry(password_frame, font=("Arial", 12), show="*")
        self.password_input.pack(fill="x", ipady=8)
        
        # 验证码区域
        captcha_frame = tk.Frame(main_frame)
        captcha_frame.pack(fill="x", pady=(0, 10))
        
        captcha_label = tk.Label(captcha_frame, text="验证码", font=("Arial", 12), fg="#555")
        captcha_label.pack(anchor="w")
        
        captcha_input_frame = tk.Frame(captcha_frame)
        captcha_input_frame.pack(fill="x")
        
        self.captcha_input = tk.Entry(captcha_input_frame, font=("Arial", 12))
        self.captcha_input.pack(side="left", fill="x", expand=True, ipady=8)
        
        self.captcha_image = tk.Label(captcha_input_frame, width=120, height=40, bg="#f0f0f0", bd=1, relief="solid")
        self.captcha_image.pack(side="right", padx=(10, 0))
        self.captcha_image.bind("<Button-1>", lambda e: self.get_captcha_image())
        
        # 记住用户名选项
        remember_frame = tk.Frame(main_frame)
        remember_frame.pack(fill="x", pady=(0, 20))
        
        self.remember_var = tk.BooleanVar()
        self.remember_checkbox = tk.Checkbutton(remember_frame, text="记住用户名", 
                                               variable=self.remember_var, 
                                               font=("Arial", 12), fg="#555")
        self.remember_checkbox.pack(anchor="w")
        
        # 登录按钮
        self.login_button = tk.Button(main_frame, text="登 录", 
                                     font=("Arial", 14, "bold"), 
                                     bg="#0078d7", fg="white",
                                     activebackground="#0063b1", activeforeground="white",
                                     command=self.login)
        self.login_button.pack(fill="x", ipady=10)
        
        # 版权信息
        version_info = f"版本: {self.version} | {self.copyright}"
        footer_label = tk.Label(main_frame, text=version_info, font=("Arial", 10), fg="#999")
        footer_label.pack(pady=(20, 0))
        
        # 获取验证码
        self.get_captcha_image()

    def create_function_ui(self):
        """创建功能界面"""
        # 创建顶部按钮区域
        button_frame = tk.Frame(self.function_frame)
        button_frame.pack(side="top", fill="x", pady=5)
        
        # 创建按钮（均匀分布）- 增大字体
        button_font = ('Arial', 12)  # 定义更大的字体
        
        self.self_btn_start = tk.Button(button_frame, text="启动", width=8,
                                       command=self.launch_browser_wrapper, font=button_font)
        self.self_btn_start.pack(side="left", padx=2, expand=True)

        self.self_btn_setting = tk.Button(button_frame, text="设置", width=4,
                                        command=self.show_config, font=button_font)
        self.self_btn_setting.pack(side="left", padx=2, expand=True)
        
        btn_past = tk.Button(button_frame, text="粘贴截图", width=8,
                            command=self.paste_and_analyze, font=button_font)
        btn_past.pack(side="left", padx=2, expand=True)
        
        btn_auto_input = tk.Button(button_frame, text="自动录入", width=8,
                                  command=self.auto_input, font=button_font)
        btn_auto_input.pack(side="left", padx=2, expand=True)
        
        # 添加退出按钮
        btn_logout = tk.Button(button_frame, text="退出", width=4,
                              command=self.logout, font=button_font)
        btn_logout.pack(side="left", padx=2, expand=True)
        
        # 创建主内容框架
        content_frame = tk.Frame(self.function_frame)
        content_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 使用PanedWindow来创建可调整大小的分割区域
        paned_window = tk.PanedWindow(content_frame, orient=tk.VERTICAL)
        paned_window.pack(fill="both", expand=True)
        
        # 图片显示区域
        image_frame = tk.Frame(paned_window, height=300)
        self.self_label_screenshot = tk.Label(image_frame, 
                                            text="截图显示区域，请先截图，然后点击按钮粘贴", 
                                            bg="gray")
        self.self_label_screenshot.pack(fill="both", expand=True, padx=5, pady=5)
        paned_window.add(image_frame, stretch="always")
        
        # AI输出结果区域
        text_frame = tk.Frame(paned_window, height=300)

        ai_cwb_frame = tk.Frame(text_frame)
        ai_cwb_frame.pack(fill="x", padx=5, pady=5)  # 横向填充并设置内边距
        # CMFT AI 处理标签
        tk.Label(ai_cwb_frame, text="CMFT AI 处理", anchor=tk.W).pack(side="left", padx=(0, 10))  # 左对齐，并与右侧控件留出间距
        # CWB 标签
        cwb_label = tk.Label(ai_cwb_frame, text="CWB No.")
        cwb_label.pack(side="left", padx=(10, 5))  # 左对齐，并设置左右边距
        # CWB 输入框
        self.cwb_input = tk.Entry(ai_cwb_frame)
        self.cwb_input.pack(side="left", fill="x", expand=True, ipady=8)  # 填充剩余空间

        # result
        self.result_text = tk.Text(text_frame)
        self.result_text.pack(fill="both", expand=True, padx=5, pady=5)
        paned_window.add(text_frame, stretch="always")
        
        # 设置初始分割位置（大约在中间）
        paned_window.sash_place(0, 0, 300)

    def get_captcha_image(self):
        """获取验证码图片"""
        try:
            # 显示加载中
            self.captcha_image.config(text="加载中...")
            
            # 获取验证码
            response = get_captcha()
            if response and response.get('code') == '200':
                result = response.get('result', {})
                self.captcha_id = result.get('captchaId')
                captcha_base64 = result.get('captchaBase64', '')
                
                # 解码Base64图片
                if captcha_base64:
                    # 去除Base64前缀
                    if ',' in captcha_base64:
                        captcha_base64 = captcha_base64.split(',')[1]
                    
                    image_data = base64.b64decode(captcha_base64)
                    image = Image.open(BytesIO(image_data))
                    photo = ImageTk.PhotoImage(image)
                    self.captcha_image.config(image=photo, text="")
                    self.captcha_image.image = photo  # 保持引用
                    self.logger.debug("验证码获取成功")
                else:
                    self.captcha_image.config(text="获取失败")
                    self.logger.debug("验证码图片数据为空")
            else:
                self.captcha_image.config(text="获取失败")
                error_msg = response.get('message') if response else "网络错误"
                self.logger.debug(f"获取验证码失败: {error_msg}")
        except Exception as e:
            self.captcha_image.config(text="获取失败")
            self.logger.debug(f"获取验证码异常: {str(e)}")

    def login(self):
        """登录处理"""
        username = self.username_input.get().strip()
        password = self.password_input.get()
        captcha_code = self.captcha_input.get().strip()
        
        # 验证输入
        if not username:
            messagebox.showwarning("提示", "请输入用户名")
            return
        
        if not password:
            messagebox.showwarning("提示", "请输入密码")
            return
        
        if not captcha_code:
            messagebox.showwarning("提示", "请输入验证码")
            return
        
        if not self.captcha_id:
            messagebox.showwarning("提示", "验证码已失效，请点击刷新")
            self.get_captcha_image()
            return
        
        try:
            # 显示登录中
            self.login_button.config(state="disabled", text="登录中...")
            self.root.update()
            
            # 调用登录接口
            response = auth(username, password, self.captcha_id, captcha_code)
            
            if response and response.get('code') == '200':
                self.logger.debug(f"用户 {username} 登录成功")
                
                # 保存登录信息到settings.json
                settings = {}
                if self.settings_file.exists():
                    try:
                        with open(self.settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                    except json.JSONDecodeError:
                        settings = {}
                
                # 更新登录信息
                settings['username'] = username if self.remember_var.get() else ""
                settings['token'] = response.get('result', '')
                settings['login_time'] = int(time.time())
                
                # 保存设置
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)
                
                # 创建日期目录并处理文件
                self.create_date_directory(username)
                
                # 恢复按钮状态
                self.login_button.config(state="normal", text="登 录")
                
                # 显示功能界面
                self.show_function_ui()
                
            else:
                error_msg = response.get('message', "登录失败") if response else "网络错误"
                messagebox.showwarning("登录失败", error_msg)
                self.logger.debug(f"登录失败: {error_msg}")
                
                # 刷新验证码
                self.get_captcha_image()
                
                # 恢复按钮状态
                self.login_button.config(state="normal", text="登 录")
        except Exception as e:
            messagebox.showerror("错误", f"登录过程发生错误: {str(e)}")
            self.logger.debug(f"登录异常: {str(e)}")
            
            # 恢复按钮状态
            self.login_button.config(state="normal", text="登 录")

    def create_date_directory(self, username):
        """创建日期目录并处理文件"""
        try:
            # 基础截图目录
            screenshot_dir = "screenshot"
            screenshot_history_dir = "screenshot_history"
            
            # 确保截图目录存在
            if not os.path.exists(screenshot_history_dir):
                os.makedirs(screenshot_history_dir)
                self.logger.debug(f"创建截图目录: {screenshot_history_dir}")
            
            # 获取当前日期作为目录名
            today = datetime.now().strftime("%Y-%m-%d")
            date_dir = os.path.join(screenshot_history_dir, today)
            
            # 确保日期目录存在
            if not os.path.exists(date_dir):
                os.makedirs(date_dir)
                self.logger.debug(f"创建日期目录: {date_dir}")
            
            # 确保源目录存在
            if not os.path.exists(screenshot_dir):
                os.makedirs(screenshot_dir, exist_ok=True)
                self.logger.debug(f"创建源截图目录: {screenshot_dir}")
            
            # 正确获取源文件路径
            screenshot_files = [f for f in os.listdir(screenshot_dir) 
                               if os.path.isfile(os.path.join(screenshot_dir, f)) 
                               and f.endswith(('.png', '.jpg', '.jpeg'))]
            
            for file in screenshot_files:
                src_path = os.path.join(screenshot_dir, file)
                dst_path = os.path.join(date_dir, file)

                try:
                    # 添加文件存在性检查
                    if not os.path.exists(src_path):
                        self.logger.debug(f"源文件不存在: {src_path}")
                        continue

                    # 使用复制+删除代替直接移动（更安全）
                    if os.path.exists(dst_path):
                        file_name, file_ext = os.path.splitext(file)
                        timestamp = datetime.now().strftime("%H%M%S")  # 更精确的时间戳
                        dst_path = os.path.join(date_dir, f"{file_name}_{timestamp}{file_ext}")

                    shutil.copy2(src_path, dst_path)  # 保留文件元数据
                    os.remove(src_path)  # 复制成功后删除源文件
                    self.logger.debug(f"成功移动文件: {src_path} -> {dst_path}")

                except Exception as e:
                    self.logger.debug(f"文件移动失败: {str(e)}")
                    continue
            
            # 获取日期目录中的文件数量
            date_dir_files = [f for f in os.listdir(date_dir) 
                             if os.path.isfile(os.path.join(date_dir, f)) 
                             and f.endswith(('.png', '.jpg', '.jpeg'))]
            file_count = len(date_dir_files)
            
            # 创建 data.json 文件
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data = {
                "taskId": "ocs-szx-01",
                "taskJson": json.dumps({"pieces": str(file_count)}),
                "runTime": current_time,
                "cmId": username
            }
            
            data_file_path = os.path.join(date_dir, "data.json")
            with open(data_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.debug(f"创建数据文件: {data_file_path}")

            # 调用 save_task_run_record 方法保存任务记录
            try:
                response = save_task_run_record(data)
                self.logger.debug(f"保存任务记录响应: {response}")
            except Exception as e:
                self.logger.debug(f"保存任务记录异常: {str(e)}")
            
        except Exception as e:
            self.logger.debug(f"创建日期目录或处理文件失败: {str(e)}")

    def show_function_ui(self):
        """显示功能界面"""
        self.login_frame.pack_forget()  # 隐藏登录界面
        self.function_frame.pack(fill="both", expand=True)  # 显示功能界面

    def show_login_ui(self):
        """显示登录界面"""
        self.function_frame.pack_forget()  # 隐藏功能界面
        self.login_frame.pack(fill="both", expand=True)  # 显示登录界面
        # 清空密码和验证码输入框
        self.password_input.delete(0, tk.END)
        self.captcha_input.delete(0, tk.END)
        # 刷新验证码
        self.get_captcha_image()

    def logout(self):
        """退出登录"""
        try:
            # 清除登录信息
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                
                # 保留用户名，清除token和登录时间
                if 'token' in settings:
                    settings['token'] = ""
                if 'login_time' in settings:
                    settings['login_time'] = 0
                
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)
            
            # 关闭浏览器
            if self.browser:
                self.browser.close()
                self.browser = None
                self.context = None
                self.page = None
            
            # 显示登录界面
            self.show_login_ui()
            
            messagebox.showinfo("提示", "已成功退出登录")
        except Exception as e:
            messagebox.showerror("错误", f"退出过程发生错误: {str(e)}")
            self.logger.debug(f"退出异常: {str(e)}")

    # 新增粘贴并分析的方法
    def paste_and_analyze(self):
        # self.result_text.delete("1.0", tk.END)  # 清空现有内容
        # 先执行粘贴图片
        if self.paste_image():
            # 然后执行AI分析
            self.ai_analyze_image()

    # 粘贴图片
    def paste_image(self):
        # 获取剪贴板中的图片
        img = ImageGrab.grabclipboard()

        if isinstance(img, Image.Image):  # 确保剪贴板中是图片
            # 获取 Label 的大小
            label_width = self.self_label_screenshot.winfo_width()
            label_height = self.self_label_screenshot.winfo_height()
            self.self_screenshot = img
            self.save_image()
            #img.show()
            img.thumbnail((600, 400))  # 调整图片大小
            tk_img = ImageTk.PhotoImage(img)
            # 更新 Label 显示图片
            self.self_label_screenshot.config(image=tk_img,width=label_width,height=label_height)
            self.self_label_screenshot.image = tk_img

            return True
        else:
            # self.self_label_screenshotlabel.config(text="剪贴板无图片，请先截图")
            messagebox.showwarning("警告", "剪贴板无图片，请先截图")
            return False

    # 保存图片到本地
    def save_image(self):
        # 定义文件夹名称
        folder_name = "screenshot"
        # 判断文件夹是否存在，如果不存在则创建
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)

        # 从剪切板中抓取图片
        image = self.self_screenshot

        if image:
            # 生成文件名（例如：screenshot_202310101200.png）
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            file_name = f"screenshot_{timestamp}.png"
            file_path = os.path.join(folder_name, file_name)
            # 保存图片
            image.save(file_path, "PNG")
            full_path = os.path.abspath(file_path)
            self.logger.debug(f"剪切板中的图片已保存到: {full_path}")
            self.self_screenshot_path = full_path

    # AI分析图片
    def ai_analyze_image(self):
        now_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        image = self.self_screenshot_path
        if image is None:
            messagebox.showwarning("警告", "请先截图，并执行粘贴图片操作")
            return
        
        self.result_text.delete("1.0", tk.END)  # 清空现有内容
        self.cwb_input.delete("0", tk.END)
        self.result_text.insert("1.0", now_time + "\nAI正在结合提示词分析图片，请稍后...\n\n")  # 插入新内容
        prompt_info = llm_prompt.prompt_json
        self.logger.debug("提示词："+ prompt_info)
        self.logger.debug("图片路径：" + image)
        # 异步执行，友好输出结果
        thread = threading.Thread(target=handle_call_llm, args=(self,prompt_info,image,))
        thread.start()

    # 自动录入开始
    def auto_input(self):
        if self.self_ai_result is None or self.self_ai_result == "":
            messagebox.showwarning("警告", "请先操作粘贴图片，并执行AI处理")
            return
        if not self.cwb_input.get().strip():
            messagebox.showwarning("警告", "CWB No.不能为空")
            return
        asyncio.get_event_loop().run_until_complete(fill_ocs.fill(self,self.self_ai_result))

    def launch_browser_wrapper(self):
        asyncio.get_event_loop().run_until_complete(self.launch_browser())

    async def launch_browser(self):
        try:
            self.self_btn_start.config(state="disabled", text="正在启动...")
            self.root.update()
            self.browser = await launch({
                'headless': False, # 关闭无头模式
                'devtools': False, # 打开 chromium 的 devtools
                'executablePath': self.chrome_path_config['config_info'],
                'args': [
                    '--disable-extensions',
                    '--hide-scrollbars',
                    '--disable-bundled-ppapi-flash',
                    '--mute-audio',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-gpu',
                ],
                'dumpio': True,
            })
            # await self.browser.newPage()
            pages = await self.browser.pages()
            self.page = pages[0]
            await self.page.setViewport({'width': 1920, 'height': 1080})
            self.page.setDefaultNavigationTimeout(60_000)
            await self.page.goto(self.target_url)
            self.self_btn_start.config(text="已启动")

        except Exception as e:
            self.logger.debug(f"启动浏览器过程异常: {str(e)}")
            messagebox.showerror("错误", f"启动浏览器过程出现异常: {str(e)}")
            self.self_btn_start.config(state="normal", text="启动")

    # 显示配置
    def show_config(self):
        popup_window = tk.Toplevel(self.root)
        popup_window.title("配置信息修改")
        popup_window.attributes("-topmost", True)
        popup_window.resizable(False, False)
        popup_window.grab_set()
        window_width = 400
        window_height = 300
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        # 计算居中位置
        x_position = (screen_width // 2) - (window_width // 2)
        y_position = (screen_height // 2) - (window_height // 2)
        # 设置 Toplevel 窗口的位置和大小
        popup_window.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        # 创建提示词描述（单行输入框）
        label1 = tk.Label(popup_window, text="Chrome.exe全路径位置")
        label1.pack(pady=(10, 0))
        content_text = tk.Text(popup_window, width=50, height=10)  # 多行文本框
        content_text.pack(pady=(5, 15))
        content_text.insert(tk.END, self.chrome_path_config['config_info'])
        def submit():
            content = content_text.get("1.0", tk.END)  # 获取 Text 框中的内容
            self.app_config_dao.update_config(config_id=self.chrome_path_config['id'],
                                              config_info=content.strip())
            self.chrome_path_config = self.app_config_dao.get_config_by_code('browser_path')
            popup_window.destroy()
            messagebox.showinfo("配置信息", "Chrome浏览器路径已经成功保存,请重新启动程序！！！")
            sys.exit(0)

        submit_button = tk.Button(popup_window, text="保 存", command=submit,font=("Arial", 12), width=10)
        submit_button.pack()
        self.root.wait_window(popup_window)  # 阻塞主窗口，等待弹层关闭


def handle_call_llm(app, prompt_info, image):
    try:
        result = app.llm_client.analyze_image_by_cmft_ocr(image, prompt_info)

        #执行后的json存储
        app.self_ai_result = result

        app.result_text.insert(tk.END, "执行结束，执行结果如下：\n\n")  # 在末尾追加文本
        app.result_text.insert(tk.END, result)  # 在末尾追加文本
        # 获取cwb的信息
        try:
            cwb = json.loads(result)['Air Waybill'].replace("-", "")
            app.cwb_input.insert("0", cwb)
        except Exception as e:
            app.logger.debug(e)


    except Exception as e:
        app.logger.debug(e)
        app.result_text.insert(tk.END, f"执行出错：{str(e)}\n")  # 显示错误信息