import sqlite3
from datetime import datetime

class AppConfigDB:
    def __init__(self, db_path='app_config.db'):
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        self._create_table()

    def _create_table(self):
        """创建表结构"""
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_code TEXT NOT NULL,
                config_info TEXT NOT NULL,
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                remark TEXT
            )
        ''')
        self.conn.commit()
        # 判断是否有默认配置，如果没有则添加一个默认配置
        if not self.get_config_by_code('browser_path'):
            # self.add_config('browser_path', 'C:\Program Files\Google\Chrome\Application\chrome.exe', '谷歌浏览器配置')
            self.add_config('browser_path', 'C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe', 'Edge浏览器配置')

    def add_config(self, config_code, config_info, remark=None):
        """添加配置"""
        self.cursor.execute(
            "INSERT INTO app_config (config_code, config_info, remark) VALUES (?, ?, ?)",
            (config_code, config_info, remark)
        )
        self.conn.commit()
        return self.cursor.lastrowid

    def get_all_configs(self):
        """获取所有配置"""
        self.cursor.execute("SELECT * FROM app_config")
        return [
            {
                'id': row[0],
                'config_code': row[1],
                'config_info': row[2],
                'create_time': row[3],
                'remark': row[4]
            } for row in self.cursor.fetchall()
        ]

    def get_config_by_id(self, config_id):
        """通过ID获取配置"""
        self.cursor.execute("SELECT * FROM app_config WHERE id = ?", (config_id,))
        row = self.cursor.fetchone()
        return {
            'id': row[0],
            'config_code': row[1],
            'config_info': row[2],
            'create_time': row[3],
            'remark': row[4]
        } if row else None

    def get_config_by_code(self, config_code):
        """通过配置代码获取配置"""
        self.cursor.execute("SELECT * FROM app_config WHERE config_code = ?", (config_code,))
        row = self.cursor.fetchone()
        return {
            'id': row[0],
            'config_code': row[1],
            'config_info': row[2],
            'create_time': row[3],
            'remark': row[4]
        } if row else None

    def update_config(self, config_id, **kwargs):
        """更新配置信息"""
        set_clause = ', '.join(f"{k} = ?" for k in kwargs.keys())
        values = list(kwargs.values())
        values.append(config_id)
        self.cursor.execute(
            f"UPDATE app_config SET {set_clause} WHERE id = ?",
            values
        )
        self.conn.commit()
        return self.cursor.rowcount

    def delete_config(self, config_id):
        """删除配置"""
        self.cursor.execute("DELETE FROM app_config WHERE id = ?", (config_id,))
        self.conn.commit()
        return self.cursor.rowcount

    def __del__(self):
        self.cursor.close()
        self.conn.close()