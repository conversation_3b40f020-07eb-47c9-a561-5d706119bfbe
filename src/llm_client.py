import base64
import hashlib
import hmac
import json
import os
from typing import Optional, Dict
from email.utils import formatdate
import requests
from src.utils.logger import Logger

class LLMClient:
    def __init__(self):
        # 获取默认LLM服务
        self.default_llm = os.getenv("LLM_DEFAULT", "CMFT").upper()
        # 初始化日志记录器
        self.logger = Logger.get_logger()
        # 初始化配置
        self._init_config()
    def _init_config(self):
        """初始化配置"""
        # CMFT配置
        self.cmft_access_key = os.getenv('CMFT_ACCESS_KEY', 'xNhvuMP5')
        self.cmft_secret_key = os.getenv('CMFT_SECRET_KEY', '5B53301DC23020B5AC6087DE561C761A')
        self.cmft_vlm_api_url = os.getenv('CMFT_VLM_API_URL', 'http://36.103.203.209:40110/wydoc/general_vlm_analysis')

        # CMFT OCR配置
        self.cmft_ocr_api_url = os.getenv('CMFT_OCR_API_URL', 'http://172.30.197.3:10000/OcrPlugins/core/ocr')
        self.cmft_ocr_app_id = os.getenv('CMFT_OCR_APP_ID', 'XVpWFydM')
        self.cmft_ocr_app_secret = os.getenv('CMFT_OCR_APP_SECRET', '1968311e4ff24786b8b12cd3a0faa328')
        self.cmft_ocr_app_key = os.getenv('CMFT_OCR_APP_KEY', 'OfDFTSK2')

    def analyze_image_by_cmft(self, image_path: str, prompt: str) -> Optional[Dict]:
        try:
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 构建multipart/form-data请求
            files = {
                'file': (os.path.basename(image_path), image_data, f'image/{os.path.splitext(image_path)[1][1:]}'),
                'prompt': (None, prompt)
            }

            # 计算请求头
            date = formatdate(timeval=None, localtime=False, usegmt=True)
            request_line = "POST /wydoc/general_vlm_analysis HTTP/1.1"

            # 签名数据
            signing_data = f"X-Date: {date}\nhost: opensseapi.cmft.com\n{request_line}"
            signature = hmac.new(
                self.cmft_secret_key.encode(),
                signing_data.encode(),
                hashlib.sha256
            ).digest()
            signature_base64 = base64.b64encode(signature).decode()

            # 设置请求头
            headers = {
                "X-Date": date,
                "host": "opensseapi.cmft.com",
                "Authorization": f'hmac username="{self.cmft_access_key}", algorithm="hmac-sha256", headers="X-Date host request-line", signature="{signature_base64}"',
                'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
                'Accept': '*/*'
            }

            self.logger.debug(f"发送API请求: {self.cmft_vlm_api_url}")
            # 发送请求
            response = requests.post(self.cmft_vlm_api_url, files=files, headers=headers)

            # 检查响应状态
            if response.status_code != 200:
                error_msg = f'API请求失败: {response.status_code} - {response.text}'
                self.logger.debug(error_msg)
                return 'API请求失败:'+ {response.status_code} +'-'+ {response.text}

            # 解析响应
            result = response.json()
            self.logger.debug(f"API返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            # 提取并解析JSON字符串
            try:
                if isinstance(result, dict):
                    self.logger.debug(result)
                    # 从嵌套结构中提取response字段
                    if 'data' in result and 'response' in result['data']:
                        # 提取JSON字符串
                        json_str = result['data']['response']

                        # 如果JSON字符串被包裹在```json和```中，去掉它们
                        if json_str.startswith('```json\n'):
                            json_str = json_str.replace('```json\n', '').replace('\n```', '')

                        # 解析JSON字符串
                        # final_result = json.loads(json_str)
                        #self.logger.debug(f"解析后的JSON结果: {json.dumps(final_result, ensure_ascii=False, indent=2)}")
                        # return final_result
                        return json_str
                    elif 'result' in result:
                        return result['result']

                self.logger.debug("无法从响应中提取有效的JSON结果")
                return "无法从响应中提取有效的JSON结果"

            except json.JSONDecodeError as e:
                self.logger.debug(f"JSON解析失败: {str(e)}")
                return "JSON解析失败:" + str(e)

        except Exception as e:
            self.logger.debug(f"处理失败: {str(e)}")
            return "处理失败:"+ str(e)

    def analyze_image_by_cmft_ocr(self, image_path: str, prompt: str) -> Optional[Dict]:
        """
        使用CMFT OCR API分析图片

        Args:
            image_path: 图片文件路径
            prompt: 提示词

        Returns:
            分析结果字典或错误信息
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                error_msg = f"图片文件不存在: {image_path}"
                self.logger.debug(error_msg)
                return error_msg

            # 检查文件名是否包含中文
            filename = os.path.basename(image_path)
            if any('\u4e00' <= char <= '\u9fff' for char in filename):
                error_msg = "文件名不能包含中文字符"
                self.logger.debug(error_msg)
                return error_msg

            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 构建multipart/form-data请求数据
            files = {
                'files': (filename, image_data, f'image/{os.path.splitext(image_path)[1][1:]}')
            }

            # 构建表单数据
            data = {
                'docType': 'GENERAL_VLM_EXTRACT',
                'outputType': 'ocrResult',
                'appId': self.cmft_ocr_app_id,
                'appSecret': self.cmft_ocr_app_secret,
                'appKey': self.cmft_ocr_app_key,
                'config': json.dumps({
                    "prompt": prompt,
                    "page_num": 1,
                    "model": "qwen_vl_72b"
                })
            }

            # 设置请求头
            headers = {
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'User-Agent': 'PostmanRuntime-ApipostRuntime/1.1.0'
            }

            self.logger.debug(f"发送OCR API请求: {self.cmft_ocr_api_url}")
            self.logger.debug(f"请求参数: {data}")

            # 发送请求
            response = requests.post(
                self.cmft_ocr_api_url,
                files=files,
                data=data,
                headers=headers
            )

            # 检查响应状态
            if response.status_code != 200:
                error_msg = f'OCR API请求失败: {response.status_code} - {response.text}'
                self.logger.debug(error_msg)
                return f'OCR API请求失败: {response.status_code} - {response.text}'

            # 解析响应
            result = response.json()
            self.logger.debug(f"OCR API返回结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            # 处理响应结果
            if isinstance(result, dict):
                # 根据实际API响应结构提取response数据
                try:
                    if 'values' in result and 'data' in result['values']:
                        data_list = result['values']['data']
                        if data_list and len(data_list) > 0:
                            first_data = data_list[0]
                            if 'data' in first_data and 'response' in first_data['data']:
                                response_str = first_data['data']['response']
                                self.logger.debug(f"原始response字符串: {response_str}")

                                # 清理response字符串，去除```json和```标记
                                if response_str.startswith('```json\n'):
                                    response_str = response_str.replace('```json\n', '').replace('\n```', '')
                                elif response_str.startswith('```json'):
                                    response_str = response_str.replace('```json', '').replace('```', '')
                                elif response_str.startswith('```'):
                                    # 处理其他可能的代码块标记
                                    lines = response_str.split('\n')
                                    if lines[0].startswith('```'):
                                        lines = lines[1:]  # 去掉第一行
                                    if lines and lines[-1].strip() == '```':
                                        lines = lines[:-1]  # 去掉最后一行
                                    response_str = '\n'.join(lines)

                                # 去除首尾空白字符
                                response_str = response_str.strip()

                                self.logger.debug(f"清理后的response字符串: {response_str}")
                                return response_str

                    # 如果没有找到预期的结构，返回原始结果
                    self.logger.debug("未找到预期的response结构，返回原始结果")
                    return result

                except Exception as e:
                    self.logger.debug(f"提取response数据时出错: {str(e)}")
                    return result
            else:
                return result

        except Exception as e:
            error_msg = f"OCR处理失败: {str(e)}"
            self.logger.debug(error_msg)
            return error_msg