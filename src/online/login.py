import hashlib
import time
import requests
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad,pad
import base64
import re
import os
import sys
from datetime import datetime
import logging
from src.utils.logger import Logger

app_key = "CEOS470eb8be7fc0643f9e34bce77f751dad"
app_secret = "324eeb44012c84466c20ab6d0ff8adc2"

# 初始化日志记录器
logger = Logger.get_logger()

#url_prefix = "http://localhost:8081"
url_prefix = "https://ceos.sinoair.com"

# 认证地址
url_ceos_captcha = url_prefix+"/global-auth/api/iam/captcha"
# 登录地址
url_ceos_auth = url_prefix+"/global-auth/api/iam/auth"



# 签名
def create_sign(app_key, app_secret, timestamp, param):
    md5_hash = hashlib.md5()
    if isinstance(param, dict):
        param = json.dumps(param, ensure_ascii=False)
    text = f"{timestamp}|ceos-global-auth|{param}|{app_key}|{app_secret}"
    md5_hash.update(text.encode('utf-8'))
    encrypted_text = md5_hash.hexdigest()
    return encrypted_text

# 解密
def aes_decrypt(encrypt_str, decrypt_key):
    # 确保密钥长度为16字节（AES-128）
    key = decrypt_key.ljust(32)[:32].encode('utf-8')
    # Base64解码密文
    encrypted_bytes = base64.b64decode(encrypt_str)
    # 创建AES解密对象
    cipher = AES.new(key, AES.MODE_ECB)  # 假设使用ECB模式，如果使用CBC模式需要提供IV
    # 解密数据
    decrypted_bytes = cipher.decrypt(encrypted_bytes)
    # 去除填充
    decrypted_data = unpad(decrypted_bytes, AES.block_size)
    # 返回解密后的字符串
    return decrypted_data.decode('utf-8')

# 加密
def aes_encrypt(content, key):
    # 确保密钥长度为16字节（AES-128）
    key = key.ljust(32)[:32].encode('utf-8')
    # 创建AES加密对象
    cipher = AES.new(key, AES.MODE_ECB)  # 假设使用ECB模式，如果使用CBC模式需要提供IV
    # 获取加密内容的字节数组
    byte_content = content.encode('utf-8')
    # 填充数据
    padded_content = pad(byte_content, AES.block_size)
    # 加密数据
    encrypted_bytes = cipher.encrypt(padded_content)
    # 将加密后的数据转换为Base64字符串返回
    return base64.b64encode(encrypted_bytes).decode('utf-8')


def post_json_request(url, headers, data):
    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, json=data)
        # 检查响应状态码
        response.raise_for_status()
        # 返回响应内容
        return response.json()
    except requests.exceptions.HTTPError as http_err:
        logger.debug(f"HTTP error occurred: {http_err}")
    except Exception as err:
        logger.debug(f"Other error occurred: {err}")
    return None

# 验证用户名格式
def validate_username(username):
    """
    验证用户名格式是否正确
    
    Args:
        username (str): 用户名
        
    Returns:
        tuple: (是否有效, 错误信息)
    """
    if not username:
        return False, "用户名不能为空"
    
    if not re.match(r'^\d{8}$', username):
        return False, "用户名必须是8位数字（招商随行ID）"
    
    return True, None

# 获取验证码信息
# 返回 {'messageId': None, 'code': '200', 'message': 'Success', 'result': {'captchaId': '验证码id', 'captchaBase64': '验证码的base64'}}
def get_captcha():
    timestamp = int(round(time.time() * 1000))
    data = {}
    sign = create_sign(app_key, app_secret, timestamp, data)
    headers = {
        "Content-Type": "application/json",
        "App-Key": app_key,
        "Timestamp": str(timestamp),
        "Sign": sign
    }
    response = post_json_request(url_ceos_captcha, headers, data)
    if response:
        logger.debug("Response JSON: %s", response)
    return response

# 认证
# 返回 Response JSON: {'messageId': None, 'code': '200', 'message': 'Success', 'result': 'FPxn7euXWBMApbhxB8NxiaopCeYuTUKWSfO7e6haZ8+sEdhhZRTJlPM+ZulRCuIWUmfaaIomuOPLfbLdy/vbErrTc7/0S40F6pPLtONEJnA2ioGFKHXYU7XTlJi2xEKyTK8QdNLjxUWDPHf0ZjlUJ51f4/rmAT6HcIyigxq5yKg='}
def auth(username, password, captcha_id, captcha_code):
    """
    用户认证函数
    
    Args:
        username (str): 用户名（招商随行ID，8位数字）
        password (str): 用户密码
        captcha_id (str): 验证码ID
        captcha_code (str): 用户输入的验证码
        
    Returns:
        dict: 认证结果
    """
    # 验证用户名格式
    is_valid, error_msg = validate_username(username)
    if not is_valid:
        return {
            "messageId": None,
            "code": "400",
            "message": error_msg,
            "result": None
        }
    
    timestamp = int(round(time.time() * 1000))
    md5_hash = hashlib.md5()
    text = f"{app_key}|{app_secret}|passwd"
    md5_hash.update(text.encode('utf-8'))
    pw_key = md5_hash.hexdigest()
    data = {
        "username": username,
        "password": aes_encrypt(password, pw_key),
        "captcha_id": captcha_id,
        "captcha_code": captcha_code
    }
    sign = create_sign(app_key, app_secret, timestamp, data)
    headers = {
        "Content-Type": "application/json",
        "App-Key": app_key,
        "Timestamp": str(timestamp),
        "Sign": sign
    }
    response = post_json_request(url_ceos_auth, headers, data)
    if response:
        logger.debug("Response JSON: %s", response)
    return response



if __name__ == '__main__':
    #获取验证码id和图片base64
    #get_captcha()
    auth('用户名','密码','验证码id','验证码')
    #logger.debug()
