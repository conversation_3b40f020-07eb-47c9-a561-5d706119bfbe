import hashlib
import time
import requests
import json
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad,pad
import base64
from src.utils.logger import Logger

app_key = "CEOS470eb8be7fc0643f9e34bce77f751dad"
app_secret = "324eeb44012c84466c20ab6d0ff8adc2"

#url_prefix = "http://localhost:8081"
url_prefix = "https://ceos.sinoair.com"

# 认证地址
url_ceos_captcha = url_prefix+"/global-auth/api/iam/captcha"
# 登录地址
url_ceos_auth = url_prefix+"/global-auth/api/iam/auth"

# management
# url_prefix_management = "http://localhost:8191"
url_prefix_management = "https://ceos.sinoair.com/gateway"
# task publish
url_publish_task = url_prefix_management + "/management/api/keyin/task/publish"
# get task
url_get_task_info = url_prefix_management + "/management/api/keyin/task/get"
# task page
url_get_task_page = url_prefix_management + "/management/api/keyin/task/page"
# 存储痕迹
url_task_run_record_save = url_prefix_management + "/management/api/keyin/task/run/record/save"

# 签名
def create_sign(app_key, app_secret, timestamp, param):
    md5_hash = hashlib.md5()
    if isinstance(param, dict):
        param = json.dumps(param, ensure_ascii=False)
    text = f"{timestamp}|ceos-global-auth|{param}|{app_key}|{app_secret}"
    md5_hash.update(text.encode('utf-8'))
    encrypted_text = md5_hash.hexdigest()
    return encrypted_text

# 解密
def aes_decrypt(encrypt_str, decrypt_key):
    # 确保密钥长度为16字节（AES-128）
    key = decrypt_key.ljust(32)[:32].encode('utf-8')
    # Base64解码密文
    encrypted_bytes = base64.b64decode(encrypt_str)
    # 创建AES解密对象
    cipher = AES.new(key, AES.MODE_ECB)  # 假设使用ECB模式，如果使用CBC模式需要提供IV
    # 解密数据
    decrypted_bytes = cipher.decrypt(encrypted_bytes)
    # 去除填充
    decrypted_data = unpad(decrypted_bytes, AES.block_size)
    # 返回解密后的字符串
    return decrypted_data.decode('utf-8')

# 加密
def aes_encrypt(content, key):
    # 确保密钥长度为16字节（AES-128）
    key = key.ljust(32)[:32].encode('utf-8')
    # 创建AES加密对象
    cipher = AES.new(key, AES.MODE_ECB)  # 假设使用ECB模式，如果使用CBC模式需要提供IV
    # 获取加密内容的字节数组
    byte_content = content.encode('utf-8')
    # 填充数据
    padded_content = pad(byte_content, AES.block_size)
    # 加密数据
    encrypted_bytes = cipher.encrypt(padded_content)
    # 将加密后的数据转换为Base64字符串返回
    return base64.b64encode(encrypted_bytes).decode('utf-8')


def post_json_request(url, headers, data):
    logger = Logger.get_logger()
    logger.debug(f"POST 发送请求: {url}")
    try:
        json_data = json.dumps(data, ensure_ascii=False)
        headers['Content-Type'] = 'application/json'
        # 发送POST请求
        response = requests.post(url, headers=headers, data=json_data)
        # 检查响应状态码
        response.raise_for_status()
        logger.debug(f"POST 响应: {response.json()}")
        # 返回响应内容
        return response.json()
    except requests.exceptions.HTTPError as http_err:
        logger.error(f"HTTP error occurred: {http_err}")
    except Exception as err:
        logger.error(f"Other error occurred: {err}")
    return None

# 获取验证码信息
# 返回 {'messageId': None, 'code': '200', 'message': 'Success', 'result': {'captchaId': '验证码id', 'captchaBase64': '验证码的base64'}}
def get_captcha():
    timestamp = int(round(time.time() * 1000))
    data = {}
    sign = create_sign(app_key, app_secret, timestamp, data)
    headers = {
        "Content-Type": "application/json",
        "App-Key": app_key,
        "Timestamp": str(timestamp),
        "Sign": sign
    }
    response = post_json_request(url_ceos_captcha, headers, data)
    if response:
        logger = Logger.get_logger()
        logger.debug("Response JSON:", response)
    return response

# 认证
# 返回 Response JSON: {'messageId': None, 'code': '200', 'message': 'Success', 'result': 'FPxn7euXWBMApbhxB8NxiaopCeYuTUKWSfO7e6haZ8+sEdhhZRTJlPM+ZulRCuIWUmfaaIomuOPLfbLdy/vbErrTc7/0S40F6pPLtONEJnA2ioGFKHXYU7XTlJi2xEKyTK8QdNLjxUWDPHf0ZjlUJ51f4/rmAT6HcIyigxq5yKg='}
def auth(username,password,captcha_id,captcha_code):
    timestamp = int(round(time.time() * 1000))
    md5_hash = hashlib.md5()
    text = f"{app_key}|{app_secret}|passwd"
    md5_hash.update(text.encode('utf-8'))
    pw_key = md5_hash.hexdigest()
    data = {
        "username":username,
        "password":aes_encrypt(password,pw_key),
        "captcha_id":captcha_id,
        "captcha_code":captcha_code
    }
    sign = create_sign(app_key, app_secret, timestamp, data)
    headers = {
        "Content-Type": "application/json",
        "App-Key": app_key,
        "Timestamp": str(timestamp),
        "Sign": sign
    }
    response = post_json_request(url_ceos_auth, headers, data)
    if response:
        logger = Logger.get_logger()
        logger.debug("Response JSON:", response)
    return response


# 存储执行记录
def save_task_run_record(run_record):
    logger = Logger.get_logger()
    try:
        timestamp = int(round(time.time() * 1000))
        sign = create_sign(app_key, app_secret, timestamp, run_record)
        headers = {
            "Content-Type": "application/json",
            "App-Key": app_key,
            "Timestamp": str(timestamp),
            "Sign": sign
        }
        logger.debug(f"发送请求: {url_task_run_record_save}")
        response = post_json_request(url_task_run_record_save, headers, run_record)
        logger.debug(f"响应: {response}")
        if response:
            logger = Logger.get_logger()
            logger.debug("Response JSON: %s", response)
        return response
    except Exception as e:

        logger.error(f"保存任务记录失败: {str(e)}")
        return {"code": "500", "message": str(e)}