import os
import sys
import subprocess
from pathlib import Path

def setup_playwright():
    """设置Playwright环境并确保浏览器已安装"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        application_path = os.path.dirname(sys.executable)
        browsers_path = os.path.join(application_path, "playwright-browsers")
        
        # 设置环境变量
        os.environ["PLAYWRIGHT_BROWSERS_PATH"] = browsers_path
        
        # 检查浏览器是否存在
        if not os.path.exists(browsers_path):
            print("Playwright浏览器未找到，请确保打包时包含了浏览器文件")
            return False
            
        print(f"Playwright浏览器路径设置为: {browsers_path}")
    return True 