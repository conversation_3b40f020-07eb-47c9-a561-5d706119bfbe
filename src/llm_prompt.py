
prompt_json = '''
请分析这张图片中的表单数据，以JSON格式返回字段和值。 JSON模版格式如下:
{
"Air Waybill":"",
"Number of piece": "", 
"Weight": "",
"Shipper Code":"",
"Shipper Company Name":"",
"Shipper Contact Name":"",
"Shipper Address":"",
"Shipper City":"",
"Shipper Region":"",
"Shipper Country":"",
"Shipper Phone No":"",
"Receiver Company Name":"",
"Receiver Section":"",
"Receiver Contact Name":"",
"Receiver Address":"",
"Receiver City":"",
"Receiver Region":"",
"Receiver Country":"",
"Receiver Postal Code":"",
"Receiver Phone No":""
}
只回复json的内容，不需要回复其他内容
'''