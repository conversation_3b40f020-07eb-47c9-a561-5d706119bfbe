import json
from tkinter import messagebox
import asyncio

ocs_country = {
    "JAPAN": "JP",
    "THAILAND": "TH",
    "VIETNAM": "VN",
    "KOREA": "KR",
    "SINGAPORE": "SG"
}

async def fill(self, command):
    try:
        command_map = json.loads(command)
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")
        messagebox.showwarning("Input Error", "Json格式转换失败")
        raise
    try:
        self.logger.info(("开始录入"))
        await self.page.waitForSelector('#dijit_PopupMenuBarItem_1_text > div', state='visible', timeout=3000)
        await self.page.click('#dijit_PopupMenuBarItem_1_text > div')
        await self.page.waitForSelector('#dijit_MenuItem_10_text')
        await self.page.click('#dijit_MenuItem_10_text')
        # await self.page.waitForNavigation({ 'waitUntil': 'networkidle0'})
        await self.page.waitForNavigation()
        await self.page.waitForSelector('input[name="cwbNo"]')
        # air_waybill_code = command_map.get("Air Waybill", "").replace("-", "")
        air_waybill_code = self.cwb_input.get().strip().replace("-", "")
        await self.page.type('input[name="cwbNo"]', air_waybill_code)
        await self.page.keyboard.press('Tab')
        await self.page.waitForNavigation()
        # Number of piece
        await self.page.waitForSelector('input[name="cwbGroup\\.pcs"]')
        await self.page.type('input[name="cwbGroup\\.pcs"]', command_map.get("Number of piece", ""))

        # Weight
        await self.page.waitForSelector('input[name="cwbGroup\\.totalWt"]')
        await self.page.type('input[name="cwbGroup\\.totalWt"]', command_map.get("Weight", ""))

        # shipper Customer CD(S)
        shipper_code = command_map.get("Shipper Code", "")
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpCoCd"]')
        await self.page.type('input[name="cwbGroup\\.pkpCoCd"]', shipper_code)

        if len(shipper_code) > 0:
            last_character = shipper_code[-1]
            await self.page.waitForSelector('input[name="cwbGroup\\.pkpAcctCd"')
            await self.page.type('input[name="cwbGroup\\.pkpAcctCd"]', last_character)

        # Shipper Company Name 固定值
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpNm1"]')
        await self.page.type('input[name="cwbGroup\\.pkpNm1"]', 'SHENZHEN')

        # shipper Country(E) Shipper Country 固定值
        await self.page.waitForSelector('select[name="cwbGroup\\.orgCntryCd"]')
        await self.page.click('select[name="cwbGroup\\.orgCntryCd"]')
        #await page.waitForTimeout(100)
        await self.page.select('select[name="cwbGroup\\.orgCntryCd"]', "CN")
        #await page.waitForTimeout(100)

        # shipper Contact
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpContactNm"]')
        await self.page.type('input[name="cwbGroup\\.pkpContactNm"]', command_map.get("shipperContact", ""))

        # shipper Address(F)
        shipper_address_original = command_map.get("Shipper Address", "")
        shipper_address_1, shipper_address_2 = split_address(shipper_address_original)
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpStreet1"]')
        await self.page.type('input[name="cwbGroup\\.pkpStreet1"]', shipper_address_1)
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpStreet2"]')
        await self.page.type('input[name="cwbGroup\\.pkpStreet2"]', shipper_address_2)
        #await page.waitForTimeout(100)

        # shipper City(J) 固定值
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpCityNm"]')
        await self.page.type('input[name="cwbGroup\\.pkpCityNm"]', "SHENZHEN")
        #await page.waitForTimeout(100)

        # shipper Region
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpRegionNm"]')
        await self.page.type('input[name="cwbGroup\\.pkpRegionNm"]', command_map.get("Shipper Region", ""))
        #await page.waitForTimeout(100)

        # shipper Phone No.(T) 固定值
        await self.page.waitForSelector('input[name="cwbGroup\\.pkpTel"]')
        await self.page.type('input[name="cwbGroup\\.pkpTel"]', "075582474444")
        #await page.waitForTimeout(100)

        # shipper Origin(K) 固定值
        await self.page.waitForSelector('select[name="cwbGroup\\.orgCd"]')
        await self.page.click('select[name="cwbGroup\\.orgCd"]')
        await asyncio.sleep(0.5)
        await self.page.select('select[name="cwbGroup\\.orgCd"]', "CNZHZ")

        # receiver Company(M)
        await self.page.waitForSelector('input[name="cwbGroup\\.delvNm1"]')
        await self.page.type('input[name="cwbGroup\\.delvNm1"]', command_map.get("Receiver Company Name", ""))

        # receiver Country(N) cwbGroup.dstCntryCd
        receiver_country = str(command_map.get("Receiver Country", "")).upper()
        country_value = "JP"
        if len(receiver_country) > 0 and receiver_country in ["THAILAND", "VIETNAM", "KOREA", "SINGAPORE", "JAPAN"]:
            country_value = ocs_country.get(receiver_country)
        await self.page.waitForSelector('select[name="cwbGroup\\.dstCntryCd"]')
        await self.page.click('select[name="cwbGroup\\.dstCntryCd"]')
        #await page.waitForTimeout(100)
        await self.page.select('select[name="cwbGroup\\.dstCntryCd"]', country_value)
        #await page.waitForTimeout(100)

        # receiver Postal CD
        await self.page.waitForSelector('input[name="cwbGroup\\.delvZip"]')
        await self.page.type('input[name="cwbGroup\\.delvZip"]', command_map.get("Receiver Postal Code", "").replace("-", ""))

        # receiver Section
        await self.page.waitForSelector('input[name="cwbGroup\\.delvSecNm"]')
        await self.page.type('input[name="cwbGroup\\.delvSecNm"]', command_map.get("Receiver Section", ""))

        # receiver Contact
        await self.page.waitForSelector('input[name="cwbGroup\\.delvContactNm"]')
        await self.page.type('input[name="cwbGroup\\.delvContactNm"]', command_map.get("Receiver Contact Name", ""))

        # receiver Address(Q)
        receiver_address_original = command_map.get("Receiver Address", "")
        receiver_address_1, receiver_address_2 = split_address(receiver_address_original)
        await self.page.waitForSelector('input[name="cwbGroup\\.delvStreet1"]')
        await self.page.type('input[name="cwbGroup\\.delvStreet1"]', receiver_address_1)
        await self.page.waitForSelector('input[name="cwbGroup\\.delvStreet2"]')
        await self.page.type('input[name="cwbGroup\\.delvStreet2"]', receiver_address_2)

        # receiver Phone No.(P)
        await self.page.waitForSelector('input[name="cwbGroup\\.delvTel"]')
        await self.page.type('input[name="cwbGroup\\.delvTel"]', command_map.get("Receiver Phone No", ""))


        messagebox.showinfo("Success", "已经完成录入")
    except Exception as e:
        messagebox.showwarning("Error", "连接失效，请重新执行【自动录入】按钮，或关闭程序重新启动")
        try:
            # content = await self.page.content()
            # self.logger.error(f"目标页面html信息: {content}")
            self.logger.error(f"目标页面不匹配: {str(e)}")
        except Exception as e2:
            self.logger.error(e2)
            self.logger.error(f"error: {str(e2)}")
    finally:
        self.logger.info("录入结束")



# 地址切割
def split_address(address, length=35):
    words = address.split()  # 按空格分词
    address1 = []
    address2 = []

    for word in words:
        if len(" ".join(address1) + " " + word) <= length or not address1:
            address1.append(word)
        else:
            address2.append(word)

    return " ".join(address1), " ".join(address2)