import requests
import base64
import os
import json

# 文件路径 - 请替换为实际的图像文件路径
file_path = "/Users/<USER>/Downloads/wechat_2025-07-30_132357_458.png"  # 请使用 .jpg, .png, .jpeg 等图像格式

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误: 文件不存在: {file_path}")
    exit(1)

print(f"正在处理文件: {file_path}")
print(f"文件大小: {os.path.getsize(file_path)} bytes")

# 加载文件并编码为 base64
with open(file_path, "rb") as img_file:
    file_data = img_file.read()
    b64_image = base64.b64encode(file_data).decode()

print(f"Base64编码长度: {len(b64_image)}")
print(f"Base64前50个字符: {b64_image[:50]}...")

url = "http://localhost:11434/api/generate"

headers = {
    "Content-Type": "application/json"
}

# 构建请求数据
request_data = {
    "model": "myaniu/OCRFlux-3B:Q8_0",
    "prompt": "请识别以下图像中的文字",
    "images": [b64_image],
    "stream": False  # 禁用流式响应，一次性返回完整结果
}

print(f"请求URL: http://************:11434/api/generate")
print(f"请求头: {headers}")
print(f"请求数据键: {list(request_data.keys())}")
print(f"模型: {request_data['model']}")
print(f"提示词: {request_data['prompt']}")
print(f"图片数量: {len(request_data['images'])}")
print("-" * 50)

# 请求 Ollama API
response = requests.post("http://************:11434/api/generate", headers=headers, json=request_data)

print(f"状态码: {response.status_code}")
print(f"响应头: {response.headers}")

# 先检查状态码
if response.status_code == 200:
    try:
        # 现在应该返回单个JSON对象（非流式）
        response_data = response.json()
        print(f"完整响应JSON: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        print("-" * 50)

        # 检查响应结构
        if "response" in response_data:
            print("识别结果:")
            print(response_data["response"])
        elif "error" in response_data:
            print(f"API返回错误: {response_data['error']}")
        else:
            print("响应中没有'response'字段")
            print(f"可用字段: {list(response_data.keys())}")

    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        print(f"原始响应文本: {response.text}")
        print("可能仍然是流式响应，尝试逐行解析...")

        # 如果JSON解析失败，回退到流式处理
        try:
            lines = response.text.strip().split('\n')
            full_response = ""

            for i, line in enumerate(lines):
                if line.strip():
                    try:
                        line_data = json.loads(line)
                        if "response" in line_data:
                            full_response += line_data["response"]
                    except json.JSONDecodeError:
                        continue

            if full_response:
                print("流式解析结果:")
                print(full_response)
            else:
                print("无法解析任何有效内容")

        except Exception as fallback_e:
            print(f"流式解析也失败: {fallback_e}")

    except Exception as e:
        print(f"处理响应失败: {e}")
        print(f"原始响应文本: {response.text}")
else:
    print(f"请求失败，状态码: {response.status_code}")
    print(f"错误响应: {response.text}")