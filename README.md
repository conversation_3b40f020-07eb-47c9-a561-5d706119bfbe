# 自动输入OCS系统

## 项目说明
这是一个自动化输入OCS系统的工具，使用Playwright进行浏览器自动化操作。

## 功能特性
- 自动登录OCS系统
- 自动填写表单数据
- 支持批量处理

## 安装说明
1. 安装Python 3.12或更高版本
2. 安装依赖包：`pip install -r requirements.txt`
3. 安装Playwright浏览器：`python -m playwright install`

## 使用方法
1. 直接运行可执行文件：双击`main.exe`
2. 或者通过命令行运行：`python main.py`

## 打包说明
如果需要打包为可执行文件，请按照以下步骤操作：

1. 确保已安装Nuitka：`pip install nuitka`
2. 执行打包命令：
python -m nuitka --mingw64 --standalone --follow-imports --enable-plugin=tk-inter --include-package=pyppeteer --include-package=PIL --windows-console-mode=disable --jobs=8 --output-dir=out main.py

{
"38017700992": "Air Waybill", //CWB No
"1": "Number of piece", //Pcs
"0.5": "Weight", //A-Wgt
"WYSE3528A": "Shipper code", //Customer CD(S)
"sddf": "Shipper Company Name", //Company(A)
"trkjgdkhg1": "Shipper Contact Name", //Contact
"trkjgdkhg2": "Shipper Address", //Address(F)
"trkjgdkhg3": "Shipper City", //*City(J)
"trkjgdkhg4": "Shipper Region", // Region
"trkjgdkhg5": "Shipper Country", //
"0755-85048532": "Shipper Phone No" //Phone No.(T)
"sekisui": "Receiver Company Name", //*Company(M)
"QA": "Receiver Section", // Section
"Mr Hamada": "Receiver Contact Name", //Contact
"2-1Hyakuyama": "Receiver Address", //Address(Q)
"OSAKA": "Receiver City", //City(X)
"Lop": "Receiver Region", // Region
"Japan": "Receiver Country", // Country(N)
"618-0021": "Receiver Postal Code", //Postal CD
"+81-75-961-4741": "Receiver Phone No" //Phone No.(P)
}

'''

## 🖥️ GUI客户端功能说明

### 主要功能模块
1. **用户认证系统**
   - 双因素登录（账号密码+图形验证码）
   - 自动保存登录状态（24小时有效）
   - 记住用户名功能
   - 安全退出机制

2. **核心操作界面**
   ```plaintext
   +----------------------------+
   | [启动] [粘贴截图] [自动录入] [退出] |
   +----------------------------+--------+
   | 截图显示区域                         |
   | (支持任意尺寸图片自适应显示)          |
   +--------------------------------------+
   | AI分析结果输出区                     |
   | (实时显示处理进度和结构化结果)        |
   +--------------------------------------+
   ```

3. **智能处理流程**
   ```mermaid
   graph TD
     A[截图/粘贴图片] --> B[自动保存到日期目录]
     B --> C[调用AI分析引擎]
     C --> D[生成结构化数据]
     D --> E[自动填写目标系统]
   ```

### 关键技术特性
- **浏览器自动化**：使用Playwright实现精准表单填写
- **智能图像处理**：
  - 支持剪贴板直接粘贴截图
  - 自动调整图片尺寸（最大600x400）
  - 按日期归档历史截图
- **异步处理**：AI分析在独立线程执行，避免界面卡顿

### 使用流程
1. 启动浏览器 → 导航至目标系统
2. 截图后使用`Ctrl+V`粘贴
3. 等待AI分析（约3-5秒）
4. 点击"自动录入"完成数据填写

### 配置要求



