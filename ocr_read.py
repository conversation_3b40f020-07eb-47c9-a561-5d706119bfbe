import os
import json
from pathlib import Path
from typing import List, Dict


def scan_files(input_path: str) -> List[Dict[str, str]]:
    """
    扫描指定路径下的所有文件，返回文件信息的JSON数组

    Args:
        input_path (str): 要扫描的文件夹路径

    Returns:
        List[Dict[str, str]]: 包含文件信息的JSON数组，格式为：
        [
            {
                "fileName": "文件名",
                "filePath": "文件全路径",
                "parentFileName": "父级文件名"
            }
        ]
    """
    result = []

    # 检查输入路径是否存在
    if not os.path.exists(input_path):
        print(f"错误：路径 {input_path} 不存在")
        return result

    # 检查输入路径是否为目录
    if not os.path.isdir(input_path):
        print(f"错误：路径 {input_path} 不是一个目录")
        return result

    # 使用os.walk遍历所有子目录和文件
    for root, dirs, files in os.walk(input_path):
        # 获取当前目录的父目录名
        parent_dir_name = os.path.basename(root)

        # 遍历当前目录下的所有文件
        for file in files:
            if file.endswith(".pdf"):
                # 构建完整的文件路径
                full_path = os.path.join(root, file)
                # 创建文件信息字典
                file_info = {
                    "fileName": file,
                    "filePath": full_path,
                    "parentFileName": parent_dir_name
                }

                result.append(file_info)

    return result


def scan_files_to_json(input_path: str) -> str:
    """
    扫描指定路径下的所有文件，返回JSON字符串

    Args:
        input_path (str): 要扫描的文件夹路径

    Returns:
        str: JSON格式的字符串
    """
    file_list = scan_files(input_path)
    return json.dumps(file_list, ensure_ascii=False, indent=2)


if __name__ == "__main__":
    # 示例用法
    test_path = "/Users/<USER>/Downloads/2025年周报"  # 您提到的示例路径

    # 如果测试路径不存在，使用当前目录作为示例
    if not os.path.exists(test_path):
        test_path = "."
        print(f"测试路径 /a 不存在，使用当前目录 {os.path.abspath(test_path)} 进行测试")

    print(f"正在扫描路径: {os.path.abspath(test_path)}")

    # 获取文件列表
    file_list = scan_files(test_path)

    # 打印结果
    print(f"\n找到 {len(file_list)} 个文件:")
    print(json.dumps(file_list, ensure_ascii=False, indent=2))

    # 也可以直接使用JSON字符串函数
    # json_result = scan_files_to_json(test_path)
    # print(json_result)