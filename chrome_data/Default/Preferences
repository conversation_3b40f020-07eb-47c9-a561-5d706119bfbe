{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "ack_existing_ntp_extensions": true, "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "arm64", "shortcuts_version": 7}, "autocomplete": {"retention_policy_last_version": 134}, "autofill": {"last_version_deduped": 134}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 956, "left": -1406, "maximized": false, "right": -206, "top": 57, "work_area_bottom": 1050, "work_area_left": -1920, "work_area_right": 0, "work_area_top": -125}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17230, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "dcf96ca5-821c-4f5f-beab-da2050f601db", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "134.0.6998.118"}, "gcm": {"product_category_for_subtypes": "com.chrome.macosx"}, "google": {"services": {"signin_scoped_device_id": "761e6eae-0365-4345-95b3-5a71c69666af"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "13387426605917033", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "13387426605917038", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "13387426605917039", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "13387426605917042", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13387426605916843", "recent_session_start_times": ["13387426605916843"], "session_last_active_time": "13387441227333745", "session_start_time": "13387426605916843"}, "intl": {"selected_languages": "zh-CN,zh"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "i/uf/GwqZzeHdvq0vGJiyWWPGWs9wFJvFXg1vO6B1+aktUs9pEh4iM/yCgrvhQS8DNE0izsAUrm5bAV4PpQTgw=="}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13387441237318232"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "FORMS_ANNOTATIONS": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13387441227537798", "setting": {"lastEngagementTime": 1.3387441227537782e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "134.0.6998.118", "creation_time": "13387426605868878", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13387441227537782", "last_time_obsolete_http_credentials_removed": 1742953065.900667, "last_time_password_store_metrics_reported": 1742953035.901165, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chrome 数据", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13387426605", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQl46OvbL54xcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADENKOjr2y+eMX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13387334399000000", "uma_in_sql_start_time": "13387426605911353"}, "sessions": {"event_log": [{"crashed": false, "time": "13387433145942384", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387433159532255", "type": 2, "window_count": 1}, {"crashed": false, "time": "13387439372014049", "type": 0}, {"crashed": true, "time": "13387439421469723", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13387439504199025", "type": 2, "window_count": 1}, {"crashed": true, "time": "13387440006424545", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13387440019477654", "type": 2, "window_count": 1}, {"crashed": true, "time": "13387440360181339", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 1, "time": "13387440379003447", "type": 2, "window_count": 1}, {"crashed": true, "time": "13387440382409709", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387440410323109", "type": 2, "window_count": 1}, {"crashed": false, "time": "13387440649957195", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387440673372688", "type": 2, "window_count": 1}, {"crashed": false, "time": "13387440838519919", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387440855836228", "type": 2, "window_count": 1}, {"crashed": false, "time": "13387440863046963", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387440876183792", "type": 2, "window_count": 1}, {"crashed": false, "time": "13387441227319964", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13387441246961470", "type": 2, "window_count": 1}], "session_data_status": 5}, "should_read_incoming_syncing_theme_prefs": true, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"]}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "134", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}}