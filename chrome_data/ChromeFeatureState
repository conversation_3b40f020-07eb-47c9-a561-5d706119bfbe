{"disable-features": "AutofillFixCurrentValueInImport<AutofillFixValueSemantics,AutofillFixInitialValueOfSelect<AutofillFixValueSemantics,AutofillFixValueSemantics<AutofillFixValueSemantics,BatterySaverModeAlignWakeUps<BatterySaverModeAlignWakeUps,CSSRelativeColorLateResolveAlways<CSSRelativeColorLateResolveAlways,DelayLayerTreeViewDeletionOnLocalSwap<RenderDocumentWithNavigationQueueing,DeleteStaleSessionCookiesOnStartup<StaleSessionCookieCleanup,FledgeBiddingAndAuctionNonceSupport<FledgeBiddingAndAuctionNonceSupport,FledgeConsiderKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeEnforceKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,FledgeQueryKAnonymity<ProtectedAudiencesKAnonymityEnforcementStudy,LightweightUafDetector<GwpAsan2024WinMac,LowerHighResolutionTimerThreshold<BatterySaverModeAlignWakeUps,PdfSearchify<PdfSearchify,RenderDocumentCompositorReuse<RenderDocumentWithNavigationQueueing,SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext<SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext,WebAssemblyDeopt<V8WasmDeoptCallIndirectInlining,WebAuthnUsePasskeyFromAnotherDeviceInContextMenu<WebAuthnUsePasskeyFromAnotherDeviceInContextMenu,WebUIJSErrorReportingExtended<DialDownWebUIJSErrorReportingExtended", "enable-features": "AckOnSurfaceActivationWhenInteractive<EnableImmediateDrawDuringScrollInteraction,AiSettingsPageRefresh<AiPrivacyFrameworkPhase1,*AnnotatedPageContentExtraction<AnnotatedPageContentExtraction,AudioInputConfirmReadsViaShmem<AudioInputConfirmReadsViaShmem,*AutofillAddressSuggestionsOnTyping<AutofillAddressSuggestionsOnTyping,*AutofillBetterLocalHeuristicPlaceholderSupport<AutofillBetterLocalHeuristicPlaceholderSupport,AutofillDisableLocalCardMigration<AutofillDisableLocalCardMigration,*AutofillEnableAccountStorageForIneligibleCountries<AutofillEnableAccountStorageForIneligibleCountries,AutofillEnableCvcStorageAndFilling<AutofillEnableCvcStorage,AutofillEnableCvcStorageAndFillingEnhancement<AutofillEnableCvcStorage,AutofillEnableVcnGrayOutForMerchantOptOut<AutofillEnableVcnGrayOutForMerchantOptOut,*AutofillGreekRegexes<AutofillGreekRegexes,*AutofillImprovedLabels<AutofillImprovedLabels,AutofillIncludeUrlInCrowdsourcing<AutofillIncludeUrlInCrowdsourcing,*AutofillInferLabelFromDefaultSelectText<AutofillInferLabelFromDefaultSelectText,AutofillParseEmailLabelAndPlaceholder<AutofillParseEmailLabelAndPlaceholder,AutofillRemovePaymentsButterDropdown<UnoDesktopM0Followup,*AutofillSupportPhoneticNameForJP<AutofillSupportPhoneticNameForJP,*AutofillUseFRAddressModel<AutofillI18nFRAddressModel,*AutofillUseNLAddressModel<AutofillI18nNLAddressModel,*AutofillVoteWhenInactive<AutofillVoteWhenInactive,AvoidResourceRequestCopies<NetworkServiceThroughputStudy,*BackgroundTabLoadingFromPerformanceManager<BackgroundTabLoadingFromPerformanceManager,BatchUploadDesktop<BatchUploadDesktop,*BookmarksUseBinaryTreeInTitledUrlIndex<BookmarksUseBinaryTreeInTitledUrlIndex,*CCSlimming<CCSlimming,CPUMeasurementInFreezingPolicy<FreezingOnBatterySaverTweaks,CacheControlNoStoreEnterBackForwardCache<BackForwardCacheForPageWithCacheControlNotStoredHeader,*CameraMicPreview<CameraMicPreview,Canvas2DHibernation<CanvasHibernationExperiments,Canvas2DHibernationReleaseTransferMemory<CanvasHibernationExperiments,*CastStreamingMediaVideoEncoder<CastStreamingMediaVideoEncoder,*CastStreamingVp9<CastStreamingVp9,ClearCanvasResourcesInBackground<CanvasHibernationExperiments,ClearUndecryptablePasswords<ClearUndecryptablePasswords,ClickToCapturedPointer<ClickToCapturedPointer,ClientSideDetectionAcceptHCAllowlist<ClientSideDetectionAcceptHCAllowlist,*ClientSideDetectionBrandAndIntentForScamDetection<ClientSideDetectionBrandAndIntentForScamDetection,ClientSideDetectionSamplePing<ClientSideDetectionSamplePing,*ClientSideDetectionSendLlamaForcedTriggerInfo<ClientSideDetectionSendLlamaForcedTriggerInfo,ComposeUpfrontInputModes<ComposeUpfrontInputModes,*CompressParkableStrings<DisableCompressParkableStrings,*ConditionalImageResize<ConditionalImageResize,*CreateURLLoaderPipeAsync<CreateURLLoaderPipeAsync,CustomProfileStringsForSupervisedUsers<KidsProfilePhase2,CustomizableSelect<CustomizableSelect,DeleteOrphanLocalStorageOnStartup<StaleStorageCleanup,DeleteStaleLocalStorageOnStartup<StaleStorageCleanup,DrawImmediatelyWhenInteractive<EnableImmediateDrawDuringScrollInteraction,*DrawQuadSplitLimit<OcclusionCullingQuadSplitLimit,EnableBandADealSupport<ProtectedAudienceBAndASelectedReportingId,EnableBandAPrivateAggregation<ProtectedAudienceBAndAPrivateAggregationAndSampleDebugReports,EnableBandASampleDebugReports<ProtectedAudienceBAndAPrivateAggregationAndSampleDebugReports,*EnableCertManagementUIV2EditCerts<EnableCertManagementUIV2Write,*EnableCertManagementUIV2Write<EnableCertManagementUIV2Write,EnableExtensionsPermissionsForSupervisedUsersOnDesktop<ExtensionParentalControlsOnLinuxMacWindows,*EnableFingerprintingProtectionFilter<FingerprintingProtectionFilter,EnableSupervisedUserSkipParentApprovalToInstallExtensions<ExtensionParentalControlsOnLinuxMacWindows,*EncryptAllPasswordsWithOSCryptAsync<NewOSCryptAlgorithmForPasswords,*EnterpriseFileObfuscation<EnterpriseFileObfuscation,*EnterpriseUpdatedProfileCreationScreen<EnterpriseUpdatedProfileCreationScreen,*EscapeLtGtInAttributes<EscapeLtGtInAttributes,EventTimingSelectionAutoScrollNoInteractionId<EventTimingSelectionAutoScrollNoInteractionId,EvictOrphanQuotaStorage<StaleStorageCleanup,EvictStaleQuotaStorage<StaleStorageCleanup,EvictionUnlocksResources<CanvasHibernationExperiments,ExpandCompositedCullRect<ExpandCompositedCullRectDesktop,ExpiredHistogramLogic<ExpiredHistograms,*ExtendedReportingRemovePrefDependency<ExtendedReportingRemovePrefDependency,ExtensionManifestV2Disabled<ExtensionManifestV2Deprecation,ExtensionManifestV3NetworkSpeechSynthesis<ExtensionManifestV3NetworkSpeechSynthesis,*ExtremeLightweightUAFDetector<ExtremeLightweightUAFDetector,*FeedbackIncludeVariations<FeedbackIncludeVariations,*FingerprintingProtectionUx<FingerprintingProtectionFilter,*FledgeAuctionDealSupport<ProtectedAudienceDealsSupport,*FledgeBidderUseBalancingThreadSelector<ProtectedAudienceBidderUseBalancingThreadSelector,*FledgeEagerJSCompilation<ProtectedAudienceEagerJSCompilation,FledgeFacilitatedTestingSignalsHeaders<FledgeFacilitatedTestingSignalsHeaders,*FledgePrepareBidderContextsInAdvance<ProtectedAudiencePrepareBidderContextsStudy,*FledgePrepareSellerContextsInAdvance<ProtectedAudiencePrepareSellerContextsStudy,*FledgeSellerWorkletThreadPool<ProtectedAudienceMultiThreadedSellerWorklet,*FledgeStartAnticipatoryProcesses<ProtectedAudienceEarlyProcessCreationStudy,*FledgeTrustedSignalsKVv1CreativeScanning<ProtectedAudienceTrustedSignalsKVv1CreativeScanning,FledgeTrustedSignalsKVv2Support<ProtectedAudienceTrustedKVSupport,FledgeUsePreconnectCache<ProtectedAudiencePreconnectCacheStudy,FreezingOnBatterySaver<FreezingOnBatterySaver,FullscreenPermanentThinController<MacPermanentThinController,*GCOnArrayBufferAllocationFailure<GCOnArrayBufferAllocationFailure,*GetUserMediaDeferredDeviceSettingsSelection<CameraMicPreview,GwpAsanMalloc<GwpAsan2024WinMac,HappinessTrackingSurveysForSecurityPage<SecurityPageHats,HttpsFirstModeV2ForEngagedSites<HttpsFirstModeV2ForEngagedSites,*HttpsFirstModeV2ForTypicallySecureUsers<HttpsFirstModeV2ForTypicallySecureUsers,IPH_SupervisedUserProfileSignin<KidsProfilePhase2,ImprovedSettingsUIOnDesktop<UnoDesktopM0Followup,ImprovedSigninUIOnDesktop<UnoDesktopM0Followup,*InitImageDecodeLastUseTime<InitImageDecodeLastUseTime,InputClosesSelect<CustomizableSelect,KeepAliveInBrowserMigration<KeepAliveInBrowserMigrationHoldback,*KeyboardLockPrompt<KeyboardLockPrompt,LCPCriticalPathPredictor<LCPPImageLoadingPriority,*LayoutNGShapeCache<LayoutNGShapeCache,LocationProviderManager<MacPlatformLocationProvider,LowPriorityAsyncScriptExecution<LowPriorityAsyncScriptExecution,*MacAccessibilityAPIMigration<MacAccessibilityAPIMigration,MacAccessibilityOptimizeChildrenChanged<MacAccessibilityOptimizeChildrenChanged,MacAllowBackgroundingRenderProcesses<PMProcessPriorityPolicy,MediaRecorderUseMediaVideoEncoder<MediaRecorderUseMediaVideoEncoder,MoveThemePrefsToSpecifics<MoveThemePrefsToSpecifics,NewContentForCheckerboardedScrolls<NewContentForCheckerboardedScrolls,OutlineSilhouetteIcon<UnoDesktopM0Followup,PMProcessPriorityPolicy<PMProcessPriorityPolicy,*PartitionAllocEventuallyZeroFreedMemory<PartialPageZeroing,PartitionVisitedLinkDatabaseWithSelfLinks<PartitionVisitedLinkDatabaseWithSelfLinks,*PassHistogramSharedMemoryOnLaunch<PassHistogramSharedMemoryOnLaunch,*PasswordFormClientsideClassifier<PasswordFormClientsideClassifier,*PasswordFormGroupedAffiliations<PasswordFormGroupedAffiliations,PasswordLeakToggleMove<PasswordLeakToggleMove,PdfCr23<PdfCr23,*PdfInk2<PdfInkSignatures,*PdfOopif<PdfOutOfProcessIframe,*PermissionsPromptSurvey<GranitePermissionsHaTS,PrefetchNewWaitLoop<PrefetchNewWaitLoop,PrefetchReusable<PrefetchReusable,Prerender2FallbackPrefetchSpecRules<Prerender2FallbackPrefetchSpecRules,PreserveDiscardableImageMapQuality<PreserveDiscardableImageMapQuality,*PreventDuplicateImageDecodes<SpeculativeImageDecodes,PriorityOverridePendingViews<PMProcessPriorityPolicy,*PrivacySandboxAdsApiUxEnhancements<PrivacySandboxAdsApiUxEnhancements,PrivacySandboxEqualizedPromptButtons<PrivacySandboxEqualizedPromptButtons,PrivacySandboxPrivacyPolicy<PrivacySandboxPrivacyPolicy,PrivateAggregationApiProtectedAudienceAdditionalExtensions<ProtectedAudienceMorePAggMetrics,PwaNavigationCapturing<PWANavigationCapturingV2WindowMacLinux,QueueNavigationsWhileWaitingForCommit<RenderDocumentWithNavigationQueueing,QuicDoesNotUseFeatures<QUIC,*ReadAnythingReadAloud<ReadAnythingReadAloudDesktop,*RecordFreezingEligibilityUKM<RecordFreezingEligibilityUKM,RedWarningSurvey<RedWarningSurvey,RenderDocument<RenderDocumentWithNavigationQueueing,RenderPassDrawnRect<RenderPassDrawnRect,*ResolutionBasedDecoderPriority<ResolutionBasedDecoderPriority,ReuseDetectionBasedOnPasswordHashes<ReuseDetectionBasedOnPasswordHashes,SafeBrowsingDeepScanningCriteria<SafeBrowsingDeepScanningCriteria,*SafeBrowsingHashPrefixRealTimeLookupsSamplePing<ExtendedReportingRemovePrefDependency,*SafetyHubHaTSOneOffSurvey<SafetyHubOneOffHats,*SafetyHubServicesOnStartUp<SafetyHubServicesOnStartUp,SavePasswordHashFromProfilePicker<SavePasswordHashFromProfilePicker,*ScrimForTabModal<ScrimForTabModal,SelectParserRelaxation<CustomizableSelect,*SendExplicitDecodeRequestsImmediately<SpeculativeImageDecodes,SendTabToSelfIOSPushNotifications<SendTabToSelfIOSPushNotifications,SharingDisableVapid<SharingDisableVapid,ShowKiteForSupervisedUsers<KidsProfilePhase2,SimpleCachePrioritizedCaching<SimpleCachePrioritizedCaching,*SiteInstanceGroupsForDataUrls<SiteInstanceGroupsForDataUrls,SkiaGraphite<SkiaGraphite,*SpeculativeImageDecodes<SpeculativeImageDecodes,*StopExportDFCMetrics<StopExportDFCMetricsExperiment,SubframeProcessReuseThresholds<SubframeProcessReuseThresholds,SyncAutofillWalletCredentialData<AutofillEnableCvcStorage,*ThrottleFrameRateOnManyDidNotProduceFrame<ThrottleFrameRateOnManyDidNotProduceFrame,UMAPseudoMetricsEffect<UMA-Pseudo-Metrics-Effect-Injection-25-Percent,Ukm<UKM,UkmSamplingRate<UkmSamplingRate,UpdatedSupervisedUserExtensionApprovalStrings<ExtensionParentalControlsOnLinuxMacWindows,*UseFrameIntervalDecider<NonAndroidUseFrameIntervalDecider,UseNewAlpsCodepointHttp2<ALPSNewCodepoint,UseNewAlpsCodepointQUIC<ALPSNewCodepoint,*UseNewEncryptionMethod<NewOSCryptAlgorithmForPasswords,UseRustJsonParser<UseRustJsonParserInCurrentSequence,V8Flag_minor_gc_task_with_lower_priority<V8MinorGcTaskWithLowerPriority,*V8Flag_safepoint_bump_qos_class<V8SafepointBumpQoSClass,*V8Flag_zero_unused_memory<PartialPageZeroing,*V8GCSpeedUsesCounters<V8GCSpeedUsesCounters,V8SeparateGCPhases<V8SeparateGCPhases,V8UpdateLimitAfterLoading<V8UpdateLimitAfterLoading,WaitForLateScrollEvents<WaitForLateScrollEvents,WebAssemblyInliningCallIndirect<V8WasmDeoptCallIndirectInlining,WebAudioBypassOutputBuffering<WebAudioBypassOutputBuffering,WebContentsDiscard<WebContentsDiscard", "force-fieldtrial-params": "BackForwardCacheForPageWithCacheControlNotStoredHeader.EnabledLaunch:level/restore-unless-cookie-change/ttl/3m,ExpandCompositedCullRectDesktop.EnabledLaunch:dpr_coef/1/pixels/4000/small_scroller_opt/true,ExpiredHistograms.ExpiryEnabledWithAllowlist:allowlist/Settings%2ETrackedPreferenceCleared%2CSettings%2ETrackedPreferenceInitialized%2CSettings%2ETrackedPreferenceMigratedLegacyDeviceId%2CSettings%2ETrackedPreferenceNullInitialized%2CSettings%2ETrackedPreferenceTrustedInitialized%2CSettings%2ETrackedPreferenceUnchanged%2CSettings%2ETrackedPreferenceCleared%2EFromRegistry%2CSettings%2ETrackedPreferenceInitialized%2EFromRegistry%2CSettings%2ETrackedPreferenceMigratedLegacyDeviceId%2EFromRegistry%2CSettings%2ETrackedPreferenceNullInitialized%2EFromRegistry%2CSettings%2ETrackedPreferenceTrustedInitialized%2EFromRegistry%2CSettings%2ETrackedPreferenceUnchanged%2EFromRegistry,FreezingOnBatterySaverTweaks.LowCPUThreshold_20250320:freezing_high_cpu_proportion/0%2E05,GwpAsan2024WinMac.RS2_v6:RendererAllocationSamplingMultiplier/600/RendererAllocationSamplingRange/10,HttpsFirstModeV2ForEngagedSites.EnabledLaunch:http-add-threshold/1/http-remove-threshold/5/https-add-threshold/80/https-remove-threshold/75,LCPPImageLoadingPriority.HandleDynamicallyAddedImages_20250304:lcpp_adjust_image_load_priority/true/lcpp_adjust_image_load_priority_override_first_n_boost/true/lcpp_enable_image_load_priority_for_htmlimageelement/true/lcpp_enable_perf_improvements/true/lcpp_histogram_sliding_window_size/1000/lcpp_image_load_priority/medium/lcpp_max_element_locator_length/1024/lcpp_max_histogram_buckets/10/lcpp_max_hosts_to_track/100/lcpp_recorded_lcp_element_types/all,LowPriorityAsyncScriptExecution.DynamicallyInsertedAndAsyncScript_20250122:delay_async_exec_opt_out_auto_fetch_priority_hint/false/delay_async_exec_opt_out_high_fetch_priority_hint/true/delay_async_exec_opt_out_low_fetch_priority_hint/false/low_pri_async_exec_cross_site_only/true/low_pri_async_exec_disable_when_lcp_not_in_html/false/low_pri_async_exec_exclude_document_write/true/low_pri_async_exec_exclude_non_parser_inserted/false/low_pri_async_exec_feature_limit/3s/low_pri_async_exec_lower_task_priority/low/low_pri_async_exec_main_frame_only/true/low_pri_async_exec_target/both/low_pri_async_exec_timeout/1s,MacPlatformLocationProvider.Enabled_20250304:LocationProviderManagerMode/HybridPlatform,PWANavigationCapturingV2WindowMacLinux.Default:link_capturing_state/reimpl_on_via_client_mode,PrefetchReusable.EnabledWithBodySizeLimit4096KiB:PrefetchReusableUseNewWaitLoop/true/prefetch_reusable_body_size_limit/4194304,Prerender2FallbackPrefetchSpecRules.Enabled_ReusableUseIfIsLikelyAheadOfPrerender4096KiB_20250226:kPrerender2FallbackBodySizeLimit/4194304/kPrerender2FallbackPrefetchReusablePolicy/UseIfIsLikelyAheadOfPrerender,QUIC.EnabledOriginFrameDec2024:channel/F/connection_options/NIPA/epoch/30000000/retransmittable_on_wire_timeout_milliseconds/200,RedWarningSurvey.RedInterstitial_20250205:RedWarningSurveyDidProceedFilter/TRUE%2CFALSE/RedWarningSurveyTriggerId/ZdMdCoDc50ugnJ3q1cK0VwqVnKb6/probability/1,RenderDocumentWithNavigationQueueing.Default_20250311:level/subframe/queueing_level/full,SecurityPageHats.Enabled_20250205_50:probability/1/security-page-time/15s/security-page-trigger-id/c4dvJ3Sz70ugnJ3q1cK0SkwJZodD,SkiaGraphite.Enabled_ARM_20250307:dawn_backend_validation/false/dawn_skip_validation/true,SubframeProcessReuseThresholds.Enabled512MB_Launched:SubframeProcessReuseMemoryThreshold/536870912,UKM.Enabled_20180314:WhitelistEntries/AboutThisSiteStatus%2CAbusiveExperienceHeuristic%2CAbusiveExperienceHeuristic%2EJavaScriptDialog%2CAbusiveExperienceHeuristic%2ETabUnder%2CAbusiveExperienceHeuristic%2EWindowOpen%2CAccessibility%2EImageDescriptions%2CAccessibility%2ERenderer%2CAccuracyTipDialog%2CAccuracyTipStatus%2CAdFrameLoad%2CAdPageLoad%2CAdsIntervention%2ELastIntervention%2CAmpPageLoad%2CAndroid%2EDarkTheme%2EAutoDarkMode%2CAndroid%2EMultiWindowChangeActivity%2CAndroid%2EMultiWindowState%2CAndroid%2EScreenRotation%2CAndroid%2EUserRequestedUserAgentChange%2CAppListAppClickData%2CAppListAppLaunch%2CAppListNonAppImpression%2CAutofill%2ECardUploadDecision%2CAutofill%2EDeveloperEngagement%2CAutofill%2EEditedAutofilledFieldAtSubmission%2CAutofill%2EFieldFillStatus%2CAutofill%2EFieldTypeValidation%2CAutofill%2EFormEvent%2CAutofill%2EFormFillSuccessIOS%2CAutofill%2EFormSubmitted%2CAutofill%2EHiddenRepresentationalFieldSkipDecision%2CAutofill%2EInteractedWithForm%2CAutofill%2ERepeatedServerTypePredictionRationalized%2CAutofill%2ESelectedMaskedServerCard%2CAutofill%2ESuggestionFilled%2CAutofill%2ESuggestionsShown%2CAutofill%2ETextFieldDidChange%2CAutofillAssistant%2EInChromeTriggering%2CAutofillAssistant%2ELiteScriptFinished%2CAutofillAssistant%2ELiteScriptOnboarding%2CAutofillAssistant%2ELiteScriptShownToUser%2CAutofillAssistant%2ELiteScriptStarted%2CAutofillAssistant%2ETiming%2CBackForwardCacheDisabledForRenderFrameHostReason%2CBackForwardCacheDisallowActivationReason%2CBackgroundFetch%2CBackgroundFetchDeletingRegistration%2CBackgroundSyncCompleted%2CBackgroundSyncRegistered%2CBadging%2CBlink%2EContextMenu%2EImageSelection%2CBlink%2EFindInPage%2CBlink%2EHTMLParsing%2CBlink%2EPageLoad%2CBlink%2EPaintTiming%2CBlink%2EScript%2EAsyncScripts%2CBlink%2EUpdateTime%2CBlink%2EUseCounter%2CBloatedRenderer%2CChromeOSApp%2EInstalledApp%2CChromeOSApp%2ELaunch%2CChromeOSApp%2EUninstallApp%2CChromeOSApp%2EUsageTime%2CClickInput%2CClientRenderingAPI%2CCompositor%2ERendering%2CCompositor%2EUserInteraction%2CContactsPicker%2EShareStatistics%2CContentIndex%2EAdded%2CContentIndex%2EDeletedByUser%2CContentIndex%2EOpened%2CContextMenuAndroid%2ESelected%2CContextMenuAndroid%2EShown%2CContextualSearch%2CContextualSuggestions%2CCPUUsageMeasurement%2CCrossOriginSubframeWithoutEmbeddingControl%2CDataReductionProxy%2CDetachedWindows%2EExperimental%2CDocument%2EOutliveTimeAfterShutdown%2CDocumentCreated%2CDownload%2ECompleted%2CDownload%2EInterrupted%2CDownload%2EResumed%2CDownload%2EStarted%2CEvent%2EScrollBegin%2ETouch%2CEvent%2EScrollBegin%2EWheel%2CEvent%2EScrollUpdate%2ETouch%2CEvent%2EScrollUpdate%2EWheel%2CExtensions%2ECrossOriginFetchFromContentScript3%2CExtensions%2EWebRequest%2EKeepaliveRequestFinished%2CFileSystemAPI%2EWebRequest%2CFlocPageLoad%2CFontMatchAttempts%2CGeneratedNavigation%2CGoogleDocsOfflineExtension%2CGraphics%2ESmoothness%2EEventLatency%2CGraphics%2ESmoothness%2ELatency%2CGraphics%2ESmoothness%2ENormalizedPercentDroppedFrames%2CGraphics%2ESmoothness%2EPercentDroppedFrames%2CGraphics%2ESmoothness%2EThroughput%2CHistoryClusters%2CHistoryManipulationIntervention%2CHistoryNavigation%2CIdentifiability%2CInputEvent%2CInputMethod%2EAssistive%2EMatch%2CInputMethod%2ENonCompliantApi%2CInstalledRelatedApps%2CIntervention%2EDocumentWrite%2EScriptBlock%2CIOS%2EFindInPageSearchMatches%2CIOS%2EIsDefaultBrowser%2CIOS%2EPageAddedToReadingList%2CIOS%2EPageReadability%2CIOS%2EPageZoomChanged%2CIOS%2ERendererGone%2CIOS%2EURLMismatchInLegacyAndSlimNavigationManager%2CJavascriptFrameworkPageLoad%2CLayout%2EDisplayCutout%2EStateChanged%2CLiteVideo%2CLoadCountsPerTopLevelDocument%2CLoadingPredictor%2CLocalNetworkRequests%2CLoginDetection%2CLookalikeUrl%2ENavigationSuggestion%2CMainFrameDownload%2CMainFrameNavigation%2CMedia%2EAutoplay%2EAttempt%2CMedia%2EAutoplay%2EAudioContext%2CMedia%2EAutoplay%2EMuted%2EUnmuteAction%2CMedia%2EBasicPlayback%2CMedia%2EEME%2EApiPromiseRejection%2CMedia%2EEME%2ECreateMediaKeys%2CMedia%2EEME%2ERequestMediaKeySystemAccess%2CMedia%2EEngagement%2ESessionFinished%2CMedia%2EEngagement%2EShortPlaybackIgnored%2CMedia%2EFeed%2EDiscover%2CMedia%2EGlobalMediaControls%2EActionButtonPressed%2CMedia%2EKaleidoscope%2ENavigation%2CMedia%2ELearning%2EPredictionRecord%2CMedia%2ESiteMuted%2CMedia%2EVideoDecodePerfRecord%2CMedia%2EWatchTime%2CMedia%2EWebAudio%2EAudioContext%2EAudibleTime%2CMedia%2EWebMediaPlayerState%2CMediaRouter%2ECastWebSenderExtensionLoadUrl%2CMediaRouter%2ESiteInitiatedMirroringStarted%2CMediaRouter%2ETabMirroringStarted%2CMemory%2EExperimental%2CMemory%2ETabFootprint%2CMixedContentAutoupgrade%2EResourceRequest%2CMobileFriendliness%2CMobileMenu%2EDirectShare%2CMobileMenu%2EFindInPage%2CMobileMenu%2EShare%2CNavigationPredictorAnchorElementMetrics%2CNavigationPredictorPageLinkClick%2CNavigationPredictorPageLinkMetrics%2CNavigationPredictorRendererWarmup%2CNavigationTiming%2CNet%2ELegacyTLSVersion%2CNoStatePrefetch%2CNotification%2COfflineMeasurements%2COfflinePages%2ESavePageRequested%2COmniboxSecurityIndicator%2EFormSubmission%2COptimizationGuide%2COptimizationGuideAutotuning%2CPageContentAnnotations%2CPageDomainInfo%2CPageForegroundSession%2CPageInfoBubble%2CPageLoad%2CPageLoad%2EFromGoogleSearch%2CPageLoad%2EInternal%2CPageLoad%2EServiceWorkerControlled%2CPageLoad%2ESignedExchange%2CPageLoadCapping%2CPageWithPassword%2CPaintPreviewCapture%2CPasswordForm%2CPasswordManager%2EWellKnownChangePasswordResult%2CPaymentApp%2ECheckoutEvents%2CPaymentRequest%2ECheckoutEvents%2CPaymentRequest%2ETransactionAmount%2CPepper%2EBroker%2CPerfectHeuristics%2CPerformanceAPI%2ELongTask%2CPerformanceAPI%2EMemory%2CPerformanceAPI%2EMemory%2ELegacy%2CPeriodicBackgroundSyncEventCompleted%2CPeriodicBackgroundSyncRegistered%2CPermission%2CPermissionUsage%2CPlugins%2EFlashInstance%2CPopup%2EClosed%2CPopup%2EPage%2CPortal%2EActivate%2CPostMessage%2EIncoming%2EFrame%2CPostMessage%2EIncoming%2EPage%2CPowerUsageScenariosIntervalData%2CPrefetchProxy%2CPrefetchProxy%2EAfterSRPClick%2CPrefetchProxy%2EPrefetchedResource%2CPrerenderPageLoad%2CPreviews%2CPreviewsCoinFlip%2CPreviewsDeferAllScript%2CPreviewsResourceLoadingHints%2CPublicImageCompressionDataUse%2CPublicImageCompressionImageLoad%2CPWA%2EVisit%2CReaderModeActivated%2CReaderModeReceivedDistillability%2CRendererSchedulerTask%2CRenderViewContextMenu%2EUsed%2CResponsiveness%2EUserInteraction%2CResponsivenessMeasurement%2CSameSiteDifferentSchemeRequest%2CSameSiteDifferentSchemeResponse%2CSchemefulSameSiteContextDowngrade%2CScreenBrightness%2CSecurity%2ESafetyTip%2CSecurity%2ESiteEngagement%2CSharedHighlights%2ELinkGenerated%2CSharedHighlights%2ELinkOpened%2CSharing%2EClickToCall%2CShopping%2EChromeCart%2CShopping%2EFormSubmitted%2CShopping%2EWillSendRequest%2CSiteIsolation%2EXSD%2EBrowser%2EBlocked%2CSmartCharging%2CSMSReceiver%2CSSL%2EMixedContentShown%2CSSL%2EMixedContentShown2%2CSubframeDownload%2CSubresourceFilter%2CSubresourceRedirect%2EPublicSrcVideoCompression%2CTab%2ERendererOOM%2CTab%2EScreenshot%2CTabManager%2EBackground%2EFirstAlertFired%2CTabManager%2EBackground%2EFirstAudioStarts%2CTabManager%2EBackground%2EFirstFaviconUpdated%2CTabManager%2EBackground%2EFirstNonPersistentNotificationCreated%2CTabManager%2EBackground%2EFirstTitleUpdated%2CTabManager%2EBackground%2EForegroundedOrClosed%2CTabManager%2EExperimental%2EBackgroundTabOpening%2ETabSwitchLoadStopped%2CTabManager%2EExperimental%2ESessionRestore%2EForegroundTab%2EPageLoad%2CTabManager%2EExperimental%2ESessionRestore%2ETabSwitchLoadStopped%2CTabManager%2ELifecycleStateChange%2CTabManager%2ETabLifetime%2CTabManager%2ETabMetrics%2CTabManager%2EWindowMetrics%2CTouchToFill%2EShown%2CTranslate%2CTranslatePageLoad%2CTrustedWebActivity%2ELocationDelegation%2CTrustedWebActivity%2EOpen%2CTrustedWebActivity%2EQualityEnforcementViolation%2CUnload%2CUserActivity%2CUserActivityId%2CUserSettingsEvent%2CV8%2EWasm%2EModuleCompiled%2CV8%2EWasm%2EModuleDecoded%2CV8%2EWasm%2EModuleInstantiated%2CV8%2EWasm%2EModuleTieredUp%2CVirtualKeyboard%2EOpen%2CWebAPK%2EInstall%2CWebAPK%2ESessionEnd%2CWebAPK%2EUninstall%2CWebAPK%2EVisit%2CWebApp%2EDailyInteraction%2CWebOTPImpact%2CWebRTC%2EAddressHarvesting%2CWebRTC%2EComplexSdp%2CWorker%2EClientAdded%2CXR%2EPageSession%2CXR%2EWebXR%2CXR%2EWebXR%2EPresentationSession%2CXR%2EWebXR%2ESession%2CXR%2EWebXR%2ESessionRequest,UMA-Pseudo-Metrics-Effect-Injection-25-Percent.SmallEffect_12_20241105:multiplicative_factor/1%2E002,UkmSamplingRate.Downsampled_202309:AbandonedSRPNavigation/5/AbusiveExperienceHeuristic%2EJavaScriptDialog/5/AdFrameLoad/90/AdPageLoad/16/AdsInterestGroup%2EAuctionLatency%2EV2/5/AdsIntervention%2ELastIntervention/2/Android%2EMultiWindowState/10/Autofill%2EDeveloperEngagement/16/Autofill%2EFieldFillStatus/60/Autofill%2EFieldTypeValidation/200/Autofill%2EFormEvent/18/Autofill%2ETextFieldDidChange/3/BTM%2EShortVisit/8/Blink%2EDeveloperMetricsRare/45/Blink%2EFedCm/2/Blink%2EFedCmIdp/2/Blink%2EFrameLoader/1300/Blink%2EHTMLParsing/35/Blink%2EJavaScriptFramework%2EVersions/3/Blink%2EPageLoad/50/Blink%2EPaintTiming/270/Blink%2EUpdateTime/90/BrowsingTopics%2EDocumentBrowsingTopicsApiResult2/850/BrowsingTopics%2EPageLoad/3/ChargeEventHistory/6/ChromeOSApp%2EInputEvent/ChromeOSApp/ChromeOSApp%2EInstalledApp/ChromeOSApp/ChromeOSApp%2ELaunch/ChromeOSApp/ChromeOSApp%2EUninstallApp/ChromeOSApp/ChromeOSApp%2EUsageTime/ChromeOSApp/ChromeOSApp%2EUsageTimeReusedSourceId/ChromeOSApp/ClientHints%2EAcceptCHFrameUsage/330/ClientHints%2EAcceptCHHeaderUsage/120/ClientHints%2EDelegateCHMetaUsage/4/ClientRenderingAPI/850/Compose%2ETextElementUsage/4/ContentManagementSystemPageLoad/40/Cookies%2EBlocked%2EDueToOriginMismatch/160/Cookies%2EFirstPartyPartitionedInCrossSiteContextV3/1100/CrossOriginSubframeWithoutEmbeddingControl/220/DIPS%2EChainBegin/6/DIPS%2EChainEnd/12/DIPS%2ENavigationFlowNode/4/DIPS%2ERedirect/20/DIPS%2ETrustIndicator%2EDirectNavigationV2/3/DailyChargeSummary/8/DocumentCreated/300/Download%2ECompleted/Download/Download%2EInterrupted/Download/Download%2EResumed/Download/Download%2EStarted/Download/Event%2EScroll/330/Event%2EScrollJank%2EPredictorJank/650/Extensions%2EOnNavigation/180/Extensions%2EWebRequest%2EKeepaliveRequestFinished/270/GeneratedNavigation/4/GoogleDocsOfflineExtension/40/Graphics%2ESmoothness/30/Graphics%2ESmoothness%2EEventLatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2ELatency/Graphics%2ESmoothness/Graphics%2ESmoothness%2ENormalizedPercentDroppedFrames/Graphics%2ESmoothness/HistoryManipulationIntervention/6/InstalledRelatedApps/12/JavascriptFrameworkPageLoad/40/LoadCountsPerTopLevelDocument/50/LoadingPredictor/25/LocalNetworkRequests/20/MainFrameNavigation/2/MainFrameNavigation%2EZstdContentEncoding/4/Media/80/Media%2EAutoplay%2EAttempt/35/Media%2EBasicPlayback/Media/Media%2EEME%2EGetStatusForPolicy/18/Media%2EEME%2ERequestMediaKeySystemAccess/3/Media%2EVideoDecodePerfRecord/16/Media%2EWebAudio%2EAudioContext%2EAudibleTime/Media/Media%2EWebMediaPlayerState/Media/Memory%2EExperimental/240/Memory%2ETabFootprint/45/MixedContentAutoupgrade%2EResourceRequest/6/MobileFriendliness/4/MobileFriendliness%2ETappedBadTargets/35/Navigation%2EReceivedResponse/50/NavigationPredictor/25/NavigationPredictorAnchorElementMetrics/NavigationPredictor/NavigationPredictorPageLinkClick/NavigationPredictor/NavigationPredictorPageLinkMetrics/NavigationPredictor/NavigationPredictorPreloadOnHover/NavigationPredictor/NavigationPredictorUserInteractions/NavigationPredictor/NavigationThrottleDeferredTime/10/NavigationTiming/35/Network%2EDataUrls/240/Notification/4/OmniboxSecurityIndicator%2EFormSubmission/6/OpenerHeuristic%2EPostPopupCookieAccess/40/OptimizationGuide/120/OptimizationGuide%2EAnnotatedPageContent/2/PageContentAnnotations2/70/PageDomainInfo/35/PageForegroundSession/70/PageLoadInitiatorForAdTagging/40/PaintPreviewCapture/2/PartitionedCookiePresentV2/9100/PasswordManager%2EFirstCCTPageLoad/2/PerformanceAPI%2ELongAnimationFrame/1100/PerformanceAPI%2ELongTask/60/PerformanceAPI%2EMemory%2ELegacy/700/PerformanceManager%2EPageResourceUsage2/420/PermissionUsage/20/PermissionUsage%2ENotificationShown/12/Popup/3/Popup%2EClosed/Popup/Popup%2EPage/Popup/PowerUsageScenariosIntervalData/40/PrefetchProxy/35/PrefetchProxy%2EAfterSRPClick/12/PrefetchProxy%2EPrefetchedResource/6/Preloading%2EAttempt/PreloadingAttempt/Preloading%2EAttempt%2EPreviousPrimaryPage/PreloadingAttempt/Preloading%2EPrediction/220/Preloading%2EPrediction%2EPreviousPrimaryPage/270/RedirectHeuristic%2ECookieAccess2/5/RedirectHeuristic%2ECookieAccessThirdParty2/5/Responsiveness%2EUserInteraction/80/SamesiteRedirectContextDowngrade/12/Security%2ESiteEngagement/35/ServiceWorker%2EMainResourceLoadCompleted/18/ServiceWorker%2EOnLoad/4/Site%2EInstall/8/Site%2EManifest/8/Site%2EQuality/8/SiteFamiliarityHeuristicResult/45/SiteInstance/6/SoftNavigation/10/SubresourceFilter/45/SubresourceLoad%2EZstdContentEncoding/900/TabManager%2ETabLifetime/4/TabRevisitTracker%2ETabStateChange/45/ThirdPartyCookies%2EBreakageIndicator%2EHTTPError/60/Translate/40/TranslatePageLoad/60/Unload/35/UserPerceivedPageVisit/120/V8%2EWasm/12/V8%2EWasm%2EModuleCompiled/V8%2EWasm/V8%2EWasm%2EModuleDecoded/V8%2EWasm/V8%2EWasm%2EModuleInstantiated/V8%2EWasm/WebOTPImpact/16/WindowProxyUsage/5/Worker%2EClientAdded/2/XR%2EWebXR/2/_default_sampling/1/_webdx_features_sampling/1,UseRustJsonParserInCurrentSequence.EnabledLaunch:UseRustJsonParserInCurrentSequence/true,WaitForLateScrollEvents.DispatchScrollEventsUntilDeadline_20250224Launch:mode/DispatchScrollEventsUntilDeadline", "force-fieldtrials": "*ALPSNewCodepoint/EnabledLaunch/*AdsP4/Default/AiPrivacyFrameworkPhase1/Launch/AnnotatedPageContentExtraction/Default/*AudioInputConfirmReadsViaShmem/EnabledLaunch/AutofillAddressSuggestionsOnTyping/Default/AutofillBetterLocalHeuristicPlaceholderSupport/Default/AutofillDisableLocalCardMigration/EnabledLaunch/AutofillEnableAccountStorageForIneligibleCountries/Default/AutofillEnableCvcStorage/Launched_98/AutofillEnableVcnGrayOutForMerchantOptOut/EnabledLaunch/AutofillFixValueSemantics/Control_20250225/AutofillGreekRegexes/Default/AutofillI18nFRAddressModel/Default/AutofillI18nNLAddressModel/Default/AutofillImprovedLabels/Default/AutofillIncludeUrlInCrowdsourcing/EnabledLaunch/AutofillInferLabelFromDefaultSelectText/Default/AutofillParseEmailLabelAndPlaceholder/Enabled_10/AutofillSupportPhoneticNameForJP/Preperiod_Enabled_20250311/AutofillVoteWhenInactive/Default/BackForwardCacheForPageWithCacheControlNotStoredHeader/EnabledLaunch/*BackgroundTabLoadingFromPerformanceManager/Default/*BatchUploadDesktop/EnabledLaunch/BatterySaverModeAlignWakeUps/Control_20250312/BookmarksUseBinaryTreeInTitledUrlIndex/Default/*BoostRenderProcessForLoading/Default/*BrowserSearchGatewayTrafficMigration/Control_20250224/CCSlimming/Default/CSSRelativeColorLateResolveAlways/Disabled_EmergencyKillSwitch/CameraMicPreview/Default/CanvasHibernationExperiments/Enabled_20240923/CastStreamingMediaVideoEncoder/Default/CastStreamingVp9/Default/*ChromeChannelStable/Enabled/*ChromnientFetchSrp/Default_20250313/ClearUndecryptablePasswords/EnabledLaunch/ClickToCapturedPointer/EnabledLaunch/ClientSideDetectionAcceptHCAllowlist/EnabledLaunch/ClientSideDetectionBrandAndIntentForScamDetection/Default/ClientSideDetectionSamplePing/EnabledLaunch/ClientSideDetectionSendLlamaForcedTriggerInfo/Default/*ComposeUpfrontInputModes/EnabledLaunch/ConditionalImageResize/Default/*ContentUsesBrowserThemeColorMode/Default/CreateURLLoaderPipeAsync/Default/CustomizableSelect/Enabled_Uncontrolled_20250220/*DesktopNtpMobilePromo/Default/DialDownWebUIJSErrorReportingExtended/Default/DisableCompressParkableStrings/Default/*EnableCertManagementUIV2Write/Default/*EnableImmediateDrawDuringScrollInteraction/EnabledLaunch/*EnableLazyLoadImageForInvisiblePage/Default/*EnableTLS13EarlyData/Default/EnterpriseFileObfuscation/Default/EnterpriseUpdatedProfileCreationScreen/Default/EscapeLtGtInAttributes/Default/*EventTimingIgnorePresentationTimeFromUnexpectedFrameSource/Default/*EventTimingSelectionAutoScrollNoInteractionId/EnabledLaunch/*ExpandCompositedCullRectDesktop/EnabledLaunch/*ExpiredHistograms/ExpiryEnabledWithAllowlist/ExtendedReportingRemovePrefDependency/Default/*ExtensionManifestV2Deprecation/Enabled_SoftDeprecation_Launched_SoftDeprecation_Stable/ExtensionManifestV3NetworkSpeechSynthesis/Enabled_50/*ExtensionParentalControlsOnLinuxMacWindows/Default/*ExtremeLightweightUAFDetector/Default/*FeatureParamWithCache/Default/FeedbackIncludeVariations/Default/*FencedFramesEnableCrossOriginAutomaticBeaconData/Default/*FenderAutoPreconnectLcpOrigins/Default/FingerprintingProtectionFilter/Default/FledgeBiddingAndAuctionNonceSupport/Disabled_EmergencyKillSwitch/FledgeFacilitatedTestingSignalsHeaders/EnabledLaunch/*ForceSupervisedUserReauthenticationForYouTube/Default/FreezingOnBatterySaver/Enabled_20250320/*FreezingOnBatterySaverTweaks/LowCPUThreshold_20250320/GCOnArrayBufferAllocationFailure/Default/GranitePermissionsHaTS/Default/*GwpAsan2024WinMac/RS2_v6/HttpsFirstModeV2ForEngagedSites/EnabledLaunch/HttpsFirstModeV2ForTypicallySecureUsers/Default/*IgnoreDuplicateNavs/Preperiod69/InitImageDecodeLastUseTime/Default/*KeepAliveInBrowserMigrationHoldback/Default_20250214/KeyboardLockPrompt/Default/KidsProfilePhase2/Enabled_Launched/*LCPPDeferUnusedPreloadRequest/Default/*LCPPFontURLPredictor/Default/*LCPPImageLoadingPriority/HandleDynamicallyAddedImages_20250304/*LCPPPrefetchSubresource/Default/*LCPTimingPredictorPrerender2/Preperiod_Default/LayoutNGShapeCache/Default/*LimitedEntropyUniformityStudy20pct2A/Group_1_20241017/*LimitedEntropyUniformityStudy20pct2B/Group_1_20241017/*LimitedEntropyUniformityStudy20pct2C/Group_2_20241017/*LimitedEntropyUniformityStudy20pct2D/Group_4_20241017/*LimitedEntropyUniformityStudy20pct2E/Group_2_20241017/*LimitedEntropyUniformityStudy20pct2F/Group_1_20241017/*LowPriorityAsyncScriptExecution/DynamicallyInsertedAndAsyncScript_20250122/MacAccessibilityAPIMigration/Default/MacAccessibilityOptimizeChildrenChanged/Enabled_50/MacPermanentThinController/EnabledLaunch/*MacPlatformLocationProvider/Enabled_20250304/MediaRecorderUseMediaVideoEncoder/EnabledLaunch/MoveThemePrefsToSpecifics/EnabledLaunch/*MultipleLayerMemberRefGWSTestStudy/GroupA_20241008/*MultipleLayerMemberRefTestStudy/GroupB_20241008/*NetworkServiceThroughputStudy/Default_20250313/*NewContentForCheckerboardedScrolls/EnabledLaunch/NewOSCryptAlgorithmForPasswords/Default/NonAndroidUseFrameIntervalDecider/Default/OcclusionCullingQuadSplitLimit/Default/*OptimizationTargetClientSidePhishingGradualRollout/Default/*OptimizationTargetGeolocationPermissionPredictionsGradualRollout/Default/*OptimizationTargetNotificationPermissionPredictionsGradualRollout/Default/*PMProcessPriorityPolicy/Enabled_20241213/*PWANavigationCapturingV2WindowMacLinux/Default/*PartialPageZeroing/Default/*PartitionVisitedLinkDatabaseWithSelfLinks/EnabledWithSelfLinks_20250227/*PassHistogramSharedMemoryOnLaunch/Default/PasswordFormClientsideClassifier/Default/PasswordFormGroupedAffiliations/Default/PasswordLeakToggleMove/EnabledLaunch/PdfCr23/EnabledLaunch/PdfInkSignatures/Default/PdfOutOfProcessIframe/Default/*PdfSearchify/Control_20250214/*PrefetchNewWaitLoop/EnabledLaunch/*PrefetchReusable/EnabledWithBodySizeLimit4096KiB/*PreloadingConfig/Default_20250307/*Prerender2BookmarkBarTriggerV2/Default/*Prerender2EarlyDocumentLifecycleUpdateV2/Default/*Prerender2FallbackPrefetchSpecRules/Enabled_ReusableUseIfIsLikelyAheadOfPrerender4096KiB_20250226/*Prerender2NewTabPageTriggerV2/Default/*PreserveDiscardableImageMapQuality/EnabledLaunch/PrivacySandboxAdsApiUxEnhancements/Default/PrivacySandboxEqualizedPromptButtons/EnabledLaunch/PrivacySandboxPrivacyPolicy/EnabledLaunch/ProtectedAudienceBAndAPrivateAggregationAndSampleDebugReports/EnabledLaunch/ProtectedAudienceBAndASelectedReportingId/EnabledLaunch/ProtectedAudienceBidderUseBalancingThreadSelector/Preperiod_Default/ProtectedAudienceDealsSupport/Default/ProtectedAudienceEagerJSCompilation/Default/ProtectedAudienceEarlyProcessCreationStudy/Default/ProtectedAudienceMorePAggMetrics/EnabledLaunch/ProtectedAudienceMultiThreadedSellerWorklet/Default/*ProtectedAudienceNoWasmLazyCompilationStudy/Preperiod55/ProtectedAudiencePreconnectCacheStudy/Enabled_V4/ProtectedAudiencePrepareBidderContextsStudy/Default/ProtectedAudiencePrepareSellerContextsStudy/Default/ProtectedAudienceTrustedKVSupport/EnabledLaunch/ProtectedAudienceTrustedSignalsKVv1CreativeScanning/Default/ProtectedAudiencesKAnonymityEnforcementStudy/DisabledFiller1_20250304/*QUIC/EnabledOriginFrameDec2024/*ReadAnythingReadAloudDesktop/Default/RecordFreezingEligibilityUKM/Default/RedWarningSurvey/RedInterstitial_20250205/RenderDocumentWithNavigationQueueing/Default_20250311/RenderPassDrawnRect/EnabledLaunch/ResolutionBasedDecoderPriority/Default/*RetroactivePreperiod/Default_Mac/ReuseDetectionBasedOnPasswordHashes/EnabledLaunch/SafeBrowsingDeepScanningCriteria/EnabledLaunch/SafetyHubOneOffHats/Default/SafetyHubServicesOnStartUp/Default/SavePasswordHashFromProfilePicker/EnabledLaunch/*ScrimForBrowserWindowModal/Default/ScrimForTabModal/Default/*SearchEngineChoiceClearInvalidPref/Default/*SearchPrefetchHighPriorityPrefetches/Default/SecurityPageHats/Enabled_20250205_50/*SeedFileTrial/Default/SendTabToSelfIOSPushNotifications/Launched/*ServiceWorkerAutoPreload/Default/*ServiceWorkerAvoidMainThreadForInitializationForDesktop/Default/*ServiceWorkerStaticRouterRaceNetworkRequestPerformanceImprovement/Default/SharingDisableVapid/EnabledLaunch/*SimdutfBase64Support/Default/*SimpleCachePrioritizedCaching/EnabledLaunch/SiteInstanceGroupsForDataUrls/Default/*SkiaGraphite/Enabled_ARM_20250307/*SkipPagehideInCommitForDSENavigation/Preperiod18/SpeculativeFixForServiceWorkerDataInDidStartServiceWorkerContext/Control_10/SpeculativeImageDecodes/Default/*SpeculativeServiceWorkerWarmUp/Default/StaleSessionCookieCleanup/Control_40/StaleStorageCleanup/EnabledLaunch/StopExportDFCMetricsExperiment/Default/*SubframeProcessReuseThresholds/Enabled512MB_Launched/*SyncIncreaseSessionNudgeDelay/Default/*TabstripComboButton/Preperiod53/ThrottleFrameRateOnManyDidNotProduceFrame/Default/*UKM/Enabled_20180314/*UMA-LayerConstrained-Uniformity-DefaultEntropyLayer_DefaultEntropyStudy/group_01/*UMA-LayerConstrained-Uniformity-LowEntropyLayer_LowEntropyStudy_WithCustomSalt/group_03/*UMA-Population-Restrict/normal/UMA-Pseudo-Metrics-Effect-Injection-25-Percent/SmallEffect_12_20241105/*UMA-Uniformity-Trial-0.5-Percent-1/group_02/*UMA-Uniformity-Trial-0.5-Percent-2/group_37/*UMA-Uniformity-Trial-1-Percent/group_97/*UMA-Uniformity-Trial-10-Percent/group_01/*UMA-Uniformity-Trial-10-Percent-sanity/group_01/*UMA-Uniformity-Trial-100-Percent/group_01/*UMA-Uniformity-Trial-20-Percent/group_01/*UMA-Uniformity-Trial-20-Percent-Session/default/*UMA-Uniformity-Trial-5-Percent/group_09/*UMA-Uniformity-Trial-50-Percent/default/UkmSamplingRate/Downsampled_202309/UnoDesktopM0Followup/EnabledLaunch/*UseRustJsonParserInCurrentSequence/EnabledLaunch/V8GCSpeedUsesCounters/Default/V8MinorGcTaskWithLowerPriority/EnabledLaunch/V8SafepointBumpQoSClass/Default/*V8SeparateGCPhases/EnabledLaunch/*V8UpdateLimitAfterLoading/EnabledLaunch/*V8WasmDeoptCallIndirectInlining/InliningOnly_20241204/*VSyncAlignedPresent/Default/WaitForLateScrollEvents/DispatchScrollEventsUntilDeadline_20250224Launch/*WebAudioBypassOutputBuffering/Enabled_20250306/WebAuthnUsePasskeyFromAnotherDeviceInContextMenu/Control/WebContentsDiscard/Enabled_50/WebRTC-Bwe-LossBasedBweV2/Default"}