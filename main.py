import sys
import os
# from src.playwright_setup import setup_playwright

# 设置playwright环境
# if not setup_playwright():
#     print("Playwright设置失败，程序将退出")
#     sys.exit(1)
#
# # 获取应用程序运行路径
# if getattr(sys, 'frozen', False):
#     # 如果是打包后的可执行文件
#     application_path = os.path.dirname(sys.executable)
#     os.environ["PLAYWRIGHT_BROWSERS_PATH"] = os.path.join(application_path, "playwright-browsers")

from src import gui_client
import tkinter as tk

if __name__ == '__main__':
    target_url = "https://webwil.ocs.co.jp/ibw/EPCMG0101X00000X.do"
    title = "Auto input for OCS"
    root = tk.Tk()
    app = gui_client.AutoInputClient(root=root, target_url=target_url, title=title)
    root.mainloop()